{"standard": {"name": "标准异动表模板", "description": "适用于大多数标准格式的异动表", "field_types": {"工号": "employee_id_string", "姓名": "name_string", "部门": "text_string", "职务": "text_string", "岗位工资": "salary_float", "薪级工资": "salary_float", "津贴": "salary_float", "补贴": "salary_float", "奖金": "salary_float", "绩效": "salary_float"}, "formatting_rules": {"salary_float": {"decimal_places": 2, "thousands_separator": true, "negative_format": "parentheses"}, "employee_id_string": {"leading_zeros": true, "min_length": 6}}}, "comprehensive": {"name": "综合异动表模板", "description": "包含更多字段的综合异动表", "field_types": {"工号": "employee_id_string", "姓名": "name_string", "身份证号": "id_number_string", "部门名称": "text_string", "部门代码": "code_string", "职务": "text_string", "人员类别": "text_string", "人员类别代码": "code_string", "入职日期": "date_string", "岗位工资": "salary_float", "薪级工资": "salary_float", "津贴": "salary_float", "补贴": "salary_float", "奖金": "salary_float", "绩效": "salary_float", "扣款": "salary_float", "实发工资": "salary_float"}, "formatting_rules": {"salary_float": {"decimal_places": 2, "thousands_separator": true, "negative_format": "minus"}, "date_string": {"format": "%Y-%m-%d"}}}}
#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
P0级问题修复测试：预览验证格式化同步
测试字段类型修改后预览验证的格式化效果
"""

import sys
import os
import unittest
from unittest.mock import Mock, patch, MagicMock
from PyQt5.QtWidgets import QApplication, QComboBox, QTableWidget
from PyQt5.QtCore import Qt

# 添加项目根目录到路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..'))

from src.gui.unified_data_import_window import UnifiedDataImportWindow, UnifiedMappingConfigWidget, PreviewValidationWidget


class TestFieldTypeSyncFix(unittest.TestCase):
    """测试字段类型同步修复"""
    
    @classmethod
    def setUpClass(cls):
        """设置测试环境"""
        if not QApplication.instance():
            cls.app = QApplication([])
        else:
            cls.app = QApplication.instance()
    
    def setUp(self):
        """每个测试前的设置"""
        self.window = UnifiedDataImportWindow()
        self.mapping_widget = self.window.mapping_tab
        self.preview_widget = self.window.preview_tab
        
        # 模拟测试数据
        self.test_data = [
            {"姓名": "张三", "工资": "5000.50", "部门": "技术部"},
            {"姓名": "李四", "工资": "6000.75", "部门": "销售部"},
            {"姓名": "王五", "工资": "4500.25", "部门": "人事部"}
        ]
        
        self.test_headers = ["姓名", "工资", "部门"]
    
    def test_preview_refresh_with_formatting_method_exists(self):
        """测试refresh_preview_with_formatting方法是否存在"""
        self.assertTrue(hasattr(self.preview_widget, 'refresh_preview_with_formatting'))
    
    def test_mapping_field_type_changed_method_exists(self):
        """测试_on_mapping_field_type_changed方法是否存在"""
        self.assertTrue(hasattr(self.mapping_widget, '_on_mapping_field_type_changed'))
    
    def test_get_current_mapping_config_method(self):
        """测试get_current_mapping_config方法"""
        # 创建模拟的映射表格
        self.mapping_widget.mapping_table = QTableWidget()
        self.mapping_widget.mapping_table.setRowCount(3)
        self.mapping_widget.mapping_table.setColumnCount(7)
        
        # 模拟字段类型下拉框
        for row in range(3):
            field_type_combo = QComboBox()
            field_type_combo.addItem("文本字符串", "text_string")
            field_type_combo.addItem("工资金额", "salary_amount")
            field_type_combo.setCurrentIndex(0 if row != 1 else 1)  # 工资字段设为salary_amount
            self.mapping_widget.mapping_table.setCellWidget(row, 3, field_type_combo)
        
        # 测试获取配置
        config = self.mapping_widget.get_current_mapping_config()
        self.assertIsInstance(config, dict)
    
    @patch('src.gui.unified_data_import_window.get_formatting_engine')
    def test_preview_data_formatting(self, mock_get_engine):
        """测试预览数据格式化"""
        # 模拟格式化引擎
        mock_engine = Mock()
        mock_engine.format_value.side_effect = lambda value, field_type: f"[{field_type}]{value}"
        mock_get_engine.return_value = mock_engine
        
        # 设置字段映射
        field_mappings = {
            "姓名": {"field_type": "text_string", "data_type": "VARCHAR(100)", "is_required": False},
            "工资": {"field_type": "salary_amount", "data_type": "DECIMAL(10,2)", "is_required": True},
            "部门": {"field_type": "text_string", "data_type": "VARCHAR(100)", "is_required": False}
        }
        
        # 调用预览数据显示
        self.preview_widget.show_preview_data("测试Sheet", self.test_data, field_mappings)
        
        # 验证格式化引擎被调用
        self.assertTrue(mock_engine.format_value.called)
    
    def test_field_type_change_triggers_preview_refresh(self):
        """测试字段类型变更触发预览刷新"""
        # 设置预览数据
        self.preview_widget.current_preview_data = self.test_data
        self.preview_widget.current_sheet_name = "测试Sheet"
        
        # 模拟父窗口关系
        self.mapping_widget.setParent(self.window)
        self.preview_widget.setParent(self.window)
        
        # 模拟refresh_preview_with_formatting方法
        self.preview_widget.refresh_preview_with_formatting = Mock()
        
        # 调用字段类型变更处理
        self.mapping_widget._on_mapping_field_type_changed()
        
        # 验证预览刷新被调用
        self.preview_widget.refresh_preview_with_formatting.assert_called_once()
    
    def test_tab_switch_triggers_preview_refresh(self):
        """测试选项卡切换触发预览刷新"""
        # 设置预览数据
        self.preview_widget.current_preview_data = self.test_data
        self.preview_widget.current_sheet_name = "测试Sheet"
        
        # 模拟映射配置
        self.mapping_widget.get_current_mapping_config = Mock(return_value={
            "姓名": {"field_type": "text_string", "data_type": "VARCHAR(100)", "is_required": False},
            "工资": {"field_type": "salary_amount", "data_type": "DECIMAL(10,2)", "is_required": True}
        })
        
        # 模拟show_preview_data方法
        self.preview_widget.show_preview_data = Mock()
        
        # 模拟选项卡切换到预览验证（索引2）
        self.window._on_tab_changed(2)
        
        # 验证预览数据被重新显示
        self.preview_widget.show_preview_data.assert_called_once()
    
    def tearDown(self):
        """每个测试后的清理"""
        if hasattr(self, 'window'):
            self.window.close()


class TestFormattingEngine(unittest.TestCase):
    """测试格式化引擎"""
    
    def setUp(self):
        """设置测试环境"""
        from src.modules.data_import.formatting_engine import get_formatting_engine
        self.engine = get_formatting_engine()
    
    def test_salary_amount_formatting(self):
        """测试工资金额格式化"""
        # 测试正常数值
        result = self.engine.format_value("5000.50", "salary_amount")
        self.assertIsInstance(result, str)
        
        # 测试无效数值（应该不抛出异常）
        result = self.engine.format_value("张三", "salary_amount")
        self.assertEqual(result, "张三")  # 应该返回原值
    
    def test_text_string_formatting(self):
        """测试文本字符串格式化"""
        result = self.engine.format_value("张三", "text_string")
        self.assertEqual(result, "张三")


if __name__ == '__main__':
    # 运行测试
    unittest.main(verbosity=2)

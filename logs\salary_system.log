2025-09-02 13:27:32.019 | INFO     | src.utils.log_config:_log_initialization_info:289 | 日志系统初始化完成
2025-09-02 13:27:32.019 | INFO     | src.utils.log_config:_log_initialization_info:290 | 日志级别: INFO
2025-09-02 13:27:32.019 | INFO     | src.utils.log_config:_log_initialization_info:291 | 控制台输出: True
2025-09-02 13:27:32.024 | INFO     | src.utils.log_config:_log_initialization_info:292 | 文件输出: True
2025-09-02 13:27:32.024 | INFO     | src.utils.log_config:_log_initialization_info:298 | 日志文件路径: logs/salary_system.log
2025-09-02 13:27:32.024 | INFO     | src:<module>:20 | 月度工资异动处理系统 v1.0.0 初始化完成
2025-09-02 13:27:35.312 | INFO     | src.gui.style_manager:__init__:68 | StyleManager初始化完成
2025-09-02 13:27:35.314 | INFO     | src.gui.style_manager:apply_global_style:93 | 开始应用全局Material Design样式...
2025-09-02 13:27:35.314 | INFO     | src.gui.style_manager:apply_global_style:105 | 全局样式应用成功
2025-09-02 13:27:35.316 | INFO     | src.gui.style_manager:enable_hot_reload:416 | 样式热重载未启用（非开发环境）
2025-09-02 13:27:35.316 | INFO     | __main__:setup_app_logging:423 | 月度工资异动处理系统 v2.0.0-refactored 启动
2025-09-02 13:27:35.316 | INFO     | __main__:main:487 | 初始化核心管理器...
2025-09-02 13:27:35.316 | INFO     | src.modules.system_config.config_manager:__init__:334 | 配置管理器初始化完成，配置文件: config.json
2025-09-02 13:27:35.316 | INFO     | src.modules.system_config.config_manager:load_config:361 | 正在加载配置文件: config.json
2025-09-02 13:27:35.319 | INFO     | src.modules.system_config.config_manager:_generate_validation_result:273 | 配置文件验证通过
2025-09-02 13:27:35.319 | INFO     | src.modules.system_config.config_manager:load_config:373 | 配置文件加载成功
2025-09-02 13:27:35.319 | INFO     | src.modules.data_storage.database_manager:_initialize_database:102 | 开始初始化数据库...
2025-09-02 13:27:35.328 | INFO     | src.modules.data_storage.database_manager:_initialize_database:157 | 数据库初始化完成
2025-09-02 13:27:35.337 | INFO     | src.modules.data_storage.database_manager:__init__:98 | 数据库管理器初始化完成，数据库路径: C:\test\salary_changes\salary_changes\data\db\salary_system.db
2025-09-02 13:27:35.337 | INFO     | src.modules.system_config.config_manager:__init__:334 | 配置管理器初始化完成，配置文件: config.json
2025-09-02 13:27:35.337 | INFO     | src.modules.data_storage.dynamic_table_manager:__init__:107 | 动态表管理器初始化完成
2025-09-02 13:27:35.345 | INFO     | __main__:main:492 | 核心管理器初始化完成。
2025-09-02 13:27:35.349 | INFO     | src.core.data_source_unification_manager:__init__:128 | 数据源统一管理器初始化完成
2025-09-02 13:27:35.351 | INFO     | src.core.table_sort_state_manager:__init__:177 | 表级排序状态管理器初始化完成
2025-09-02 13:27:35.351 | INFO     | src.core.event_bus:__init__:229 | 事件总线初始化完成
2025-09-02 13:27:35.351 | INFO     | src.core.error_handler_manager:__init__:111 | 🔧 [P2-3] 错误处理管理器初始化完成
2025-09-02 13:27:35.353 | INFO     | src.core.error_handler_manager:register_recovery_strategy:372 | 🔧 [P2-3] 注册恢复策略: data_error_medium
2025-09-02 13:27:35.355 | INFO     | src.core.error_handler_manager:register_recovery_strategy:372 | 🔧 [P2-3] 注册恢复策略: ui_error_medium
2025-09-02 13:27:35.356 | INFO     | src.core.error_handler_manager:register_recovery_strategy:372 | 🔧 [P2-3] 注册恢复策略: system_error_medium
2025-09-02 13:27:35.356 | INFO     | src.gui.prototype.prototype_main_window:_register_recovery_strategies:11899 | 🔧 [P2-3] 错误恢复策略注册完成
2025-09-02 13:27:35.356 | INFO     | src.core.event_bus:__init__:229 | 事件总线初始化完成
2025-09-02 13:27:35.359 | INFO     | src.gui.prototype.prototype_main_window:_setup_error_handling:11754 | 🔧 [P2-3] 错误处理机制设置完成
2025-09-02 13:27:35.359 | INFO     | src.gui.prototype.prototype_main_window:_init_recursive_call_protection:11792 | 🔧 [P1-2] 递归调用防护机制初始化完成
2025-09-02 13:27:35.545 | INFO     | src.core.architecture_factory:__init__:62 | 架构重构工厂初始化完成
2025-09-02 13:27:35.545 | INFO     | src.core.architecture_factory:initialize_architecture:72 | 开始初始化架构重构系统...
2025-09-02 13:27:35.551 | INFO     | src.core.event_bus:__init__:229 | 事件总线初始化完成
2025-09-02 13:27:35.555 | INFO     | src.modules.data_import.config_sync_manager:_do_config_initialization:153 | 使用已存在的配置文件: state/data/field_mappings.json
2025-09-02 13:27:35.555 | INFO     | src.modules.data_import.config_sync_manager:__init__:81 | 🆕 [新架构] 配置同步管理器初始化完成（依赖注入）
2025-09-02 13:27:35.555 | INFO     | src.core.unified_state_manager:_load_state:585 | 状态已从文件加载: state/unified_state.json
2025-09-02 13:27:35.555 | INFO     | src.core.unified_state_manager:__init__:184 | 统一状态管理器初始化完成
2025-09-02 13:27:35.561 | INFO     | src.core.field_mapping_manager:_load_config:89 | 🔧 [P3优化] 字段映射配置加载成功
2025-09-02 13:27:35.569 | INFO     | src.modules.data_import.config_sync_manager:_do_config_initialization:153 | 使用已存在的配置文件: state/data/field_mappings.json
2025-09-02 13:27:35.569 | INFO     | src.modules.data_import.config_sync_manager:__init__:81 | 🆕 [新架构] 配置同步管理器初始化完成（依赖注入）
2025-09-02 13:27:35.569 | INFO     | src.modules.format_management.field_registry:__init__:95 | 🏷️ [字段注册] 字段注册系统初始化: state/data/field_mappings.json
2025-09-02 13:27:35.569 | INFO     | src.core.unified_mapping_service:__init__:45 | UnifiedMappingService 初始化完成
2025-09-02 13:27:35.573 | INFO     | src.core.unified_data_request_manager:__init__:217 | 统一数据请求管理器初始化完成
2025-09-02 13:27:35.573 | INFO     | src.core.unified_data_request_manager:__init__:217 | 统一数据请求管理器初始化完成
2025-09-02 13:27:35.575 | INFO     | src.core.unified_state_manager:_load_state:585 | 状态已从文件加载: state/unified_state.json
2025-09-02 13:27:35.575 | INFO     | src.core.unified_state_manager:__init__:184 | 统一状态管理器初始化完成
2025-09-02 13:27:35.575 | INFO     | src.services.table_data_service:__init__:84 | 表格数据服务初始化完成
2025-09-02 13:27:35.577 | INFO     | src.core.architecture_factory:initialize_architecture:103 | 🎉 架构重构系统初始化成功！耗时: 25.8ms
2025-09-02 13:27:35.619 | INFO     | src.gui.prototype.performance.data_preload_cache:__init__:46 | 数据预加载缓存初始化完成 - 最大条目数: 100, TTL: 300秒
2025-09-02 13:27:35.619 | INFO     | src.gui.prototype.performance.data_preload_cache:__init__:232 | 表格状态缓存初始化完成
2025-09-02 13:27:35.619 | INFO     | src.gui.prototype.performance.header_config_cache:__init__:32 | 表头配置缓存初始化完成
2025-09-02 13:27:35.619 | INFO     | src.gui.prototype.performance.header_config_cache:__init__:211 | 字段映射缓存初始化完成
2025-09-02 13:27:35.619 | INFO     | src.gui.prototype.performance.performance_manager:__init__:46 | 🚀 性能管理器初始化完成
2025-09-02 13:27:35.623 | INFO     | src.gui.prototype.prototype_main_window:__init__:3650 | 🚀 性能管理器已集成
2025-09-02 13:27:35.623 | INFO     | src.gui.prototype.prototype_main_window:__init__:3652 | ✅ 新架构集成成功！
2025-09-02 13:27:35.625 | INFO     | src.gui.prototype.prototype_main_window:_inject_config_sync_manager_to_existing_tables:3765 | ConfigSyncManager重新注入完成，已更新0个表格实例
2025-09-02 13:27:35.626 | INFO     | src.gui.prototype.prototype_main_window:_setup_new_architecture_listeners:3730 | ✅ 新架构事件监听器设置完成
2025-09-02 13:27:35.645 | INFO     | src.gui.widgets.pagination_cache_manager:__init__:107 | 分页缓存管理器初始化完成 - 最大条目数: 50, 最大内存: 50MB
2025-09-02 13:27:35.648 | INFO     | src.gui.prototype.managers.responsive_layout_manager:__init__:121 | 响应式布局管理器初始化完成
2025-09-02 13:27:35.651 | INFO     | src.gui.prototype.managers.communication_manager:__init__:134 | 组件通信管理器初始化完成
2025-09-02 13:27:36.050 | INFO     | src.gui.prototype.prototype_main_window:_create_menu_bar:2726 | 菜单栏创建完成
2025-09-02 13:27:36.051 | INFO     | src.gui.prototype.prototype_main_window:__init__:2701 | 菜单栏管理器初始化完成
2025-09-02 13:27:36.055 | INFO     | src.gui.table_header_manager:__init__:105 | 🔧 [P1-3] 性能优化表头管理器初始化完成
2025-09-02 13:27:36.055 | INFO     | src.gui.prototype.prototype_main_window:_setup_managers:5458 | 管理器设置完成，包含增强版表头管理器
2025-09-02 13:27:36.055 | INFO     | src.gui.prototype.prototype_main_window:_apply_window_style:5463 | 🔧 开始应用窗口级Material Design样式...
2025-09-02 13:27:36.055 | INFO     | src.gui.style_manager:apply_window_style:123 | 开始应用窗口级样式...
2025-09-02 13:27:36.060 | INFO     | src.gui.style_manager:apply_window_style:136 | 窗口级样式应用成功
2025-09-02 13:27:36.063 | INFO     | src.gui.prototype.prototype_main_window:_apply_window_style:5470 | ✅ 窗口级样式应用成功
2025-09-02 13:27:36.064 | INFO     | src.gui.prototype.prototype_main_window:_setup_responsive_style:5511 | ✅ 响应式样式监听设置完成
2025-09-02 13:27:36.069 | INFO     | src.modules.system_config.config_manager:load_config:361 | 正在加载配置文件: config.json
2025-09-02 13:27:36.078 | INFO     | src.modules.system_config.config_manager:_generate_validation_result:273 | 配置文件验证通过
2025-09-02 13:27:36.086 | INFO     | src.modules.system_config.config_manager:load_config:373 | 配置文件加载成功
2025-09-02 13:27:36.089 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:_load_state:165 | 导航状态加载成功
2025-09-02 13:27:36.090 | INFO     | src.gui.prototype.widgets.smart_search_debounce:__init__:368 | 智能防抖搜索算法初始化完成
2025-09-02 13:27:36.090 | INFO     | src.gui.prototype.widgets.smart_tree_expansion:__init__:150 | 智能树形展开算法初始化完成
2025-09-02 13:27:36.110 | INFO     | src.modules.data_storage.dynamic_table_manager:get_table_list:1333 | 🔧 [深度修复] 找到 1 个匹配类型 'salary_data' 的表 (尝试 1/5)
2025-09-02 13:27:36.111 | WARNING  | src.modules.data_storage.dynamic_table_manager:get_navigation_tree_data:1992 | 元数据项 salary_data_2025_05 缺少年份或月份信息，已跳过
2025-09-02 13:27:36.119 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:_load_fallback_salary_data:1835 | 🏠 使用示例数据结构加载导航（首次启动或暂无数据时显示）
2025-09-02 13:27:36.128 | INFO     | src.modules.data_storage.dynamic_table_manager:get_table_list:1333 | 🔧 [深度修复] 找到 4 个匹配类型 'change_data' 的表 (尝试 1/5)
2025-09-02 13:27:36.128 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:_setup_navigation_data:890 | 导航面板已重构：移除功能性导航，专注数据导航
2025-09-02 13:27:36.128 | INFO     | src.gui.prototype.widgets.smart_tree_expansion:predict_expansion_paths:216 | 预测展开路径: ['工资表 > 2025年 > 05月 > 全部在职人员', '工资表 > 2025年 > 05月 > A岗职工', '工资表', '工资表 > 2025年', '工资表 > 2025年 > 05月']
2025-09-02 13:27:36.128 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:_restore_state:924 | 恢复导航状态: 7个展开项
2025-09-02 13:27:36.146 | INFO     | src.gui.prototype.widgets.smart_tree_expansion:predict_expansion_paths:216 | 预测展开路径: ['工资表 > 2025年 > 05月 > 全部在职人员', '工资表 > 2025年 > 05月 > A岗职工', '工资表', '工资表 > 2025年', '工资表 > 2025年 > 05月']
2025-09-02 13:27:36.147 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:__init__:643 | 增强导航面板初始化完成
2025-09-02 13:27:36.149 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:_load_state:165 | 导航状态加载成功
2025-09-02 13:27:36.161 | INFO     | src.gui.prototype.widgets.smart_search_debounce:__init__:368 | 智能防抖搜索算法初始化完成
2025-09-02 13:27:36.168 | INFO     | src.gui.prototype.widgets.smart_tree_expansion:__init__:150 | 智能树形展开算法初始化完成
2025-09-02 13:27:36.174 | INFO     | src.modules.data_storage.dynamic_table_manager:get_table_list:1333 | 🔧 [深度修复] 找到 1 个匹配类型 'salary_data' 的表 (尝试 1/5)
2025-09-02 13:27:36.175 | WARNING  | src.modules.data_storage.dynamic_table_manager:get_navigation_tree_data:1992 | 元数据项 salary_data_2025_05 缺少年份或月份信息，已跳过
2025-09-02 13:27:36.198 | INFO     | src.modules.data_storage.dynamic_table_manager:get_table_list:1333 | 🔧 [深度修复] 找到 4 个匹配类型 'change_data' 的表 (尝试 1/5)
2025-09-02 13:27:36.201 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:_setup_navigation_data:890 | 导航面板已重构：移除功能性导航，专注数据导航
2025-09-02 13:27:36.213 | INFO     | src.gui.prototype.widgets.smart_tree_expansion:predict_expansion_paths:216 | 预测展开路径: ['异动人员表 > 2025年 > 12月 > 全部在职人员工资表', '异动人员表 > 2025年 > 12月 > A岗职工', '异动人员表 > 2025年 > 12月 > 退休人员工资表', '异动人员表 > 2025年 > 12月 > 离休人员工资表', '异动人员表 > 2025年 > 12月']
2025-09-02 13:27:36.215 | INFO     | src.gui.prototype.widgets.smart_tree_expansion:predict_expansion_paths:216 | 预测展开路径: ['异动人员表 > 2025年 > 12月 > 全部在职人员工资表', '异动人员表 > 2025年 > 12月 > A岗职工', '异动人员表 > 2025年 > 12月 > 退休人员工资表', '异动人员表 > 2025年 > 12月 > 离休人员工资表', '异动人员表 > 2025年 > 12月']
2025-09-02 13:27:36.221 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:__init__:643 | 增强导航面板初始化完成
2025-09-02 13:27:36.222 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:auto_select_latest_data:1396 | 🔧 [P1-2修复] 开始自动选择最新数据...
2025-09-02 13:27:36.223 | INFO     | src.modules.data_storage.dynamic_table_manager:get_latest_salary_data_path:2131 | 开始获取最新工资数据路径...
2025-09-02 13:27:36.226 | INFO     | src.modules.data_storage.dynamic_table_manager:get_table_list:1333 | 🔧 [深度修复] 找到 1 个匹配类型 'salary_data' 的表 (尝试 1/5)
2025-09-02 13:27:36.226 | WARNING  | src.modules.data_storage.dynamic_table_manager:get_latest_salary_data_path:2173 | 未找到有效的工资数据表
2025-09-02 13:27:36.227 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:_get_latest_path_with_retry:1601 | 🔧 [P1-2修复] 检查表状态失败，1.0s后重试...
2025-09-02 13:27:36.228 | WARNING  | src.gui.prototype.widgets.enhanced_navigation_panel:auto_select_latest_data:1405 | 🔧 [P1-2修复] 未找到最新工资数据路径（可能是首次启动）
2025-09-02 13:27:36.375 | INFO     | src.gui.prototype.widgets.header_update_manager:__init__:68 | HeaderUpdateManager 初始化完成，关联表格: VirtualizedExpandableTable
2025-09-02 13:27:36.376 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:__init__:2142 | 🚨 [架构修复] 启用模型数据同步机制
2025-09-02 13:27:36.381 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_register_shortcuts:1366 | 快捷键注册完成: 18/18 个
2025-09-02 13:27:36.381 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:__init__:1809 | 拖拽排序管理器初始化完成
2025-09-02 13:27:36.403 | INFO     | src.modules.data_management.data_flow_validator:__init__:85 | 🔧 [数据验证器] 初始化完成，验证级别: lenient
2025-09-02 13:27:36.405 | INFO     | src.modules.state_management.table_state_manager:__init__:103 | 🔧 [状态管理] 仅使用内存缓存
2025-09-02 13:27:36.408 | INFO     | src.modules.state_management.table_state_manager:__init__:105 | 🔧 [状态管理] 初始化完成，最大缓存: 100
2025-09-02 13:27:36.409 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:__init__:2195 | 🔧 [P0-紧急修复] 数据流验证器初始化成功（宽松模式）
2025-09-02 13:27:36.411 | INFO     | src.modules.data_import.header_edit_manager:__init__:82 | 表头编辑管理器初始化完成
2025-09-02 13:27:36.412 | INFO     | src.core.unified_state_manager:_load_state:585 | 状态已从文件加载: state/unified_state.json
2025-09-02 13:27:36.413 | INFO     | src.core.unified_state_manager:__init__:184 | 统一状态管理器初始化完成
2025-09-02 13:27:36.414 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:__init__:2247 | 🎯 [统一格式管理] 使用统一格式管理器
2025-09-02 13:27:36.428 | INFO     | src.gui.prototype.widgets.column_sort_manager:_load_field_mappings_from_config:370 | 🔧 [新架构] 成功加载 46 个字段映射
2025-09-02 13:27:36.429 | INFO     | src.gui.prototype.widgets.column_sort_manager:__init__:102 | 🆕 [新架构排序] 多列排序管理器初始化完成，最大排序列数: 3
2025-09-02 13:27:36.430 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:__init__:2294 | 🆕 [新架构多列排序] 初始化完成，支持最多3列排序
2025-09-02 13:27:36.432 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:__init__:1553 | 🔧 [列宽保存修复] 列宽管理器初始化完成
2025-09-02 13:27:36.436 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:__init__:1554 | 🔧 [列宽保存修复] 配置文件绝对路径: C:\test\salary_changes\salary_changes\state\column_widths.json
2025-09-02 13:27:36.437 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:__init__:1555 | 🔧 [列宽保存修复] 配置文件存在: True
2025-09-02 13:27:36.438 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:__init__:1556 | 🔧 [列宽保存修复] 父目录存在: True
2025-09-02 13:27:36.439 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:__init__:1557 | 🔧 [列宽保存修复] 当前工作目录: C:\test\salary_changes\salary_changes
2025-09-02 13:27:36.440 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:__init__:2301 | 列宽管理器初始化完成
2025-09-02 13:27:36.441 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:setup_ui:2428 | 🔧 [架构修复] 保持自定义排序功能，修复数据显示问题
2025-09-02 13:27:36.443 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:__init__:2315 | 虚拟化可展开表格初始化完成 - Phase 4核心交互功能增强版，最大可见行数: 1000
2025-09-02 13:27:36.444 | INFO     | src.gui.prototype.prototype_main_window:_get_default_table_name:2306 | 🔧 [兼容性修复] 暂无数据表，返回占位符（等待数据导入）
2025-09-02 13:27:36.444 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_set_data_impl:2736 | 🔧 [修复监控] 开始设置数据: 0行，表名: default_table
2025-09-02 13:27:36.445 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_validate_and_fix_data_consistency:4669 | 🔧 [数据修复] 数据或表头为空，跳过一致性验证
2025-09-02 13:27:36.476 | INFO     | src.gui.prototype.widgets.pagination_state_manager:__init__:78 | 🔧 [P1优化] 分页状态管理器初始化完成
2025-09-02 13:27:36.479 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_safe_set_column_count:7339 | 方案A：安全设置列数: 0
2025-09-02 13:27:36.480 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_set_data_impl:2838 | 🔧 [P1优化] 表切换操作: None -> default_table
2025-09-02 13:27:36.480 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:auto_adjust_max_visible_rows:5573 | 数据量为0，不调整最大可见行数，保持当前值: 1000
2025-09-02 13:27:36.491 | INFO     | src.gui.prototype.widgets.table_header_cache:__init__:67 | 🔧 [P1优化] 表头缓存管理器初始化完成，最大缓存: 100
2025-09-02 13:27:36.505 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_set_data_impl:2890 | 🔧 [P0修复] 检测到空表且表头为中文，跳过字段映射: 22个表头
2025-09-02 13:27:36.506 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_set_data_impl:2975 | 表格格式化完成: default_table, 类型: active_employees
2025-09-02 13:27:36.522 | INFO     | src.gui.prototype.widgets.optimized_table_renderer:render_with_auto_strategy:252 | [数据流追踪] 自动渲染策略选择: 0行数据
2025-09-02 13:27:36.534 | INFO     | src.gui.prototype.widgets.optimized_table_renderer:render_with_auto_strategy:256 | [数据流追踪] 检测到空数据，使用快速清空策略
2025-09-02 13:27:36.535 | INFO     | src.gui.prototype.widgets.optimized_table_renderer:render_with_auto_strategy:264 | 🔧 [P0-修复] 空数据集设置表头: 22个
2025-09-02 13:27:36.536 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_update_visible_rows:466 | 🚨 [UI数据修复] _update_visible_rows调用，数据行数: 0
2025-09-02 13:27:36.537 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:443 | 表格数据已设置: 0 行, 22 列
2025-09-02 13:27:36.540 | INFO     | src.gui.prototype.widgets.column_sort_manager:update_table_context:443 | 🔧 [新架构] 排序管理器切换到表格: default_table
2025-09-02 13:27:36.548 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_set_data_impl:3159 | 表格数据设置完成: 0 行, 耗时: 71.4ms
2025-09-02 13:27:36.557 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_restore_sort_indicators_after_data_reload:8277 | 🔧 [P0-排序修复] 开始恢复排序指示器显示
2025-09-02 13:27:36.558 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_restore_sort_indicators_after_data_reload:8290 | 🔧 [P0-排序修复] 无排序状态需要恢复
2025-09-02 13:27:36.561 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:restore_column_widths:1752 | 🔧 [P2-3修复] 列宽已恢复: default_table (22/22 列, 表头数量: 22)
2025-09-02 13:27:36.562 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_set_data_impl:3238 | 🔧 [修复监控] UI强制刷新完成，数据可见
2025-09-02 13:27:36.563 | INFO     | src.gui.prototype.prototype_main_window:_show_empty_table_fallback:2331 | 🔧 [P0-修复] 使用备用方案显示了标准空表格，表头数量: 22个，所有表头为中文
2025-09-02 13:27:36.574 | INFO     | src.gui.widgets.pagination_widget:_apply_styles:298 | 分页组件Material Design样式应用成功
2025-09-02 13:27:36.592 | INFO     | src.gui.widgets.pagination_widget:__init__:176 | ✅ [防抖升级] 智能防抖系统已启用
2025-09-02 13:27:36.593 | INFO     | src.gui.widgets.pagination_widget:__init__:184 | 分页组件初始化完成
2025-09-02 13:27:36.649 | INFO     | src.gui.prototype.prototype_main_window:_connect_control_panel_signals:594 | 控制面板按钮信号连接完成
2025-09-02 13:27:36.701 | INFO     | src.gui.prototype.prototype_main_window:_setup_shortcuts:5420 | 快捷键设置完成
2025-09-02 13:27:36.701 | INFO     | src.gui.prototype.prototype_main_window:_setup_ui:5377 | 主窗口UI设置完成。
2025-09-02 13:27:36.706 | INFO     | src.gui.prototype.prototype_main_window:_setup_connections:5614 | 🔧 [全局排序] 全局排序开关连接成功
2025-09-02 13:27:36.708 | INFO     | src.gui.prototype.prototype_main_window:_setup_connections:5646 | 🆕 [新架构排序] 使用表格组件内部的自定义排序循环，无需连接排序信号
2025-09-02 13:27:36.709 | INFO     | src.gui.prototype.prototype_main_window:_setup_connections:5658 | ✅ 已连接分页刷新信号到主窗口
2025-09-02 13:27:36.709 | INFO     | src.gui.prototype.prototype_main_window:_setup_connections:5659 | ✅ 已连接分页组件事件到新架构
2025-09-02 13:27:36.714 | INFO     | src.gui.prototype.prototype_main_window:_setup_connections:5670 | ✅ 已连接概览标签页刷新按钮到主窗口
2025-09-02 13:27:36.719 | INFO     | src.gui.prototype.prototype_main_window:_setup_connections:5673 | 信号连接设置完成
2025-09-02 13:27:36.722 | INFO     | src.gui.prototype.prototype_main_window:_load_field_mappings_from_file:6931 | 🔧 [P1-2修复] 发现 102 个表的配置
2025-09-02 13:27:36.724 | INFO     | src.modules.data_import.config_sync_manager:load_mapping:488 | 🔧 [P1-1修复] 加载统一格式映射: salary_data_2025_07_active_employees, 10个字段
2025-09-02 13:27:36.728 | INFO     | src.modules.data_import.config_sync_manager:load_mapping:488 | 🔧 [P1-1修复] 加载统一格式映射: active_employees, 5个字段
2025-09-02 13:27:36.737 | INFO     | src.modules.data_import.config_sync_manager:load_mapping:483 | 🔧 [P1-1修复] 加载旧格式映射: change_data_2025_12_全部在职人员工资表, 30个字段
2025-09-02 13:27:36.740 | INFO     | src.modules.data_import.config_sync_manager:load_mapping:483 | 🔧 [P1-1修复] 加载旧格式映射: change_data_2025_12_退休人员工资表, 30个字段
2025-09-02 13:27:36.743 | INFO     | src.modules.data_import.config_sync_manager:load_mapping:483 | 🔧 [P1-1修复] 加载旧格式映射: change_data_2025_12_A岗职工, 30个字段
2025-09-02 13:27:36.748 | INFO     | src.modules.data_import.config_sync_manager:load_mapping:483 | 🔧 [P1-1修复] 加载旧格式映射: change_data_2025_12_离休人员工资表, 30个字段
2025-09-02 13:27:36.751 | INFO     | src.modules.data_import.config_sync_manager:load_mapping:483 | 🔧 [P1-1修复] 加载旧格式映射: salary_data_2025_05, 30个字段
2025-09-02 13:27:36.754 | INFO     | src.modules.data_import.config_sync_manager:load_mapping:483 | 🔧 [P1-1修复] 加载旧格式映射: mapping_config_20250901_162809, 1个字段
2025-09-02 13:27:36.757 | INFO     | src.modules.data_import.config_sync_manager:load_mapping:483 | 🔧 [P1-1修复] 加载旧格式映射: mapping_config_20250902_084853, 21个字段
2025-09-02 13:27:36.774 | INFO     | src.modules.data_import.config_sync_manager:load_mapping:483 | 🔧 [P1-1修复] 加载旧格式映射: mapping_config_20250902_084855, 21个字段
2025-09-02 13:27:36.777 | INFO     | src.modules.data_import.config_sync_manager:load_mapping:483 | 🔧 [P1-1修复] 加载旧格式映射: mapping_config_20250902_084858, 21个字段
2025-09-02 13:27:36.780 | INFO     | src.modules.data_import.config_sync_manager:load_mapping:483 | 🔧 [P1-1修复] 加载旧格式映射: mapping_config_20250902_084900, 21个字段
2025-09-02 13:27:36.783 | INFO     | src.modules.data_import.config_sync_manager:load_mapping:483 | 🔧 [P1-1修复] 加载旧格式映射: mapping_config_20250902_084902, 21个字段
2025-09-02 13:27:36.787 | INFO     | src.modules.data_import.config_sync_manager:load_mapping:483 | 🔧 [P1-1修复] 加载旧格式映射: mapping_config_20250902_084903, 21个字段
2025-09-02 13:27:36.792 | INFO     | src.modules.data_import.config_sync_manager:load_mapping:483 | 🔧 [P1-1修复] 加载旧格式映射: mapping_config_20250902_084906, 21个字段
2025-09-02 13:27:36.793 | INFO     | src.modules.data_import.config_sync_manager:load_mapping:483 | 🔧 [P1-1修复] 加载旧格式映射: mapping_config_20250902_084907, 21个字段
2025-09-02 13:27:36.822 | INFO     | src.modules.data_import.config_sync_manager:load_mapping:483 | 🔧 [P1-1修复] 加载旧格式映射: mapping_config_20250902_084908, 21个字段
2025-09-02 13:27:36.825 | INFO     | src.modules.data_import.config_sync_manager:load_mapping:483 | 🔧 [P1-1修复] 加载旧格式映射: mapping_config_20250902_105709, 21个字段
2025-09-02 13:27:36.834 | INFO     | src.modules.data_import.config_sync_manager:load_mapping:483 | 🔧 [P1-1修复] 加载旧格式映射: mapping_config_20250902_105711, 21个字段
2025-09-02 13:27:36.838 | INFO     | src.modules.data_import.config_sync_manager:load_mapping:483 | 🔧 [P1-1修复] 加载旧格式映射: mapping_config_20250902_105712, 21个字段
2025-09-02 13:27:36.841 | INFO     | src.modules.data_import.config_sync_manager:load_mapping:483 | 🔧 [P1-1修复] 加载旧格式映射: mapping_config_20250902_105713, 21个字段
2025-09-02 13:27:36.845 | INFO     | src.modules.data_import.config_sync_manager:load_mapping:483 | 🔧 [P1-1修复] 加载旧格式映射: mapping_config_20250902_105740, 21个字段
2025-09-02 13:27:36.851 | INFO     | src.modules.data_import.config_sync_manager:load_mapping:483 | 🔧 [P1-1修复] 加载旧格式映射: mapping_config_20250902_105742, 21个字段
2025-09-02 13:27:36.854 | INFO     | src.modules.data_import.config_sync_manager:load_mapping:483 | 🔧 [P1-1修复] 加载旧格式映射: mapping_config_20250902_105744, 21个字段
2025-09-02 13:27:36.856 | INFO     | src.modules.data_import.config_sync_manager:load_mapping:483 | 🔧 [P1-1修复] 加载旧格式映射: mapping_config_20250902_105745, 21个字段
2025-09-02 13:27:36.862 | INFO     | src.modules.data_import.config_sync_manager:load_mapping:483 | 🔧 [P1-1修复] 加载旧格式映射: mapping_config_20250902_105746, 21个字段
2025-09-02 13:27:36.866 | INFO     | src.modules.data_import.config_sync_manager:load_mapping:483 | 🔧 [P1-1修复] 加载旧格式映射: mapping_config_20250902_105747, 21个字段
2025-09-02 13:27:36.869 | INFO     | src.modules.data_import.config_sync_manager:load_mapping:483 | 🔧 [P1-1修复] 加载旧格式映射: mapping_config_20250902_105748, 21个字段
2025-09-02 13:27:36.873 | INFO     | src.modules.data_import.config_sync_manager:load_mapping:483 | 🔧 [P1-1修复] 加载旧格式映射: mapping_config_20250902_113652, 21个字段
2025-09-02 13:27:36.876 | INFO     | src.modules.data_import.config_sync_manager:load_mapping:483 | 🔧 [P1-1修复] 加载旧格式映射: mapping_config_20250902_113656, 21个字段
2025-09-02 13:27:36.878 | INFO     | src.modules.data_import.config_sync_manager:load_mapping:483 | 🔧 [P1-1修复] 加载旧格式映射: mapping_config_20250902_113658, 21个字段
2025-09-02 13:27:36.880 | INFO     | src.modules.data_import.config_sync_manager:load_mapping:483 | 🔧 [P1-1修复] 加载旧格式映射: mapping_config_20250902_113704, 21个字段
2025-09-02 13:27:36.889 | INFO     | src.modules.data_import.config_sync_manager:load_mapping:483 | 🔧 [P1-1修复] 加载旧格式映射: mapping_config_20250902_113707, 21个字段
2025-09-02 13:27:36.891 | INFO     | src.modules.data_import.config_sync_manager:load_mapping:483 | 🔧 [P1-1修复] 加载旧格式映射: mapping_config_20250902_113708, 21个字段
2025-09-02 13:27:36.894 | INFO     | src.modules.data_import.config_sync_manager:load_mapping:483 | 🔧 [P1-1修复] 加载旧格式映射: mapping_config_20250902_113718, 21个字段
2025-09-02 13:27:36.897 | INFO     | src.modules.data_import.config_sync_manager:load_mapping:483 | 🔧 [P1-1修复] 加载旧格式映射: mapping_config_20250902_113719, 21个字段
2025-09-02 13:27:36.902 | INFO     | src.modules.data_import.config_sync_manager:load_mapping:483 | 🔧 [P1-1修复] 加载旧格式映射: mapping_config_20250902_113730, 21个字段
2025-09-02 13:27:36.904 | INFO     | src.modules.data_import.config_sync_manager:load_mapping:483 | 🔧 [P1-1修复] 加载旧格式映射: mapping_config_20250902_113732, 21个字段
2025-09-02 13:27:36.907 | INFO     | src.modules.data_import.config_sync_manager:load_mapping:483 | 🔧 [P1-1修复] 加载旧格式映射: mapping_config_20250902_113734, 21个字段
2025-09-02 13:27:36.910 | INFO     | src.modules.data_import.config_sync_manager:load_mapping:483 | 🔧 [P1-1修复] 加载旧格式映射: mapping_config_20250902_113740, 21个字段
2025-09-02 13:27:36.918 | INFO     | src.modules.data_import.config_sync_manager:load_mapping:483 | 🔧 [P1-1修复] 加载旧格式映射: mapping_config_20250902_113741, 21个字段
2025-09-02 13:27:36.923 | INFO     | src.modules.data_import.config_sync_manager:load_mapping:483 | 🔧 [P1-1修复] 加载旧格式映射: mapping_config_20250902_130718, 21个字段
2025-09-02 13:27:36.928 | INFO     | src.modules.data_import.config_sync_manager:load_mapping:483 | 🔧 [P1-1修复] 加载旧格式映射: mapping_config_20250902_130720, 21个字段
2025-09-02 13:27:36.932 | INFO     | src.modules.data_import.config_sync_manager:load_mapping:483 | 🔧 [P1-1修复] 加载旧格式映射: mapping_config_20250902_130724, 21个字段
2025-09-02 13:27:36.936 | INFO     | src.modules.data_import.config_sync_manager:load_mapping:483 | 🔧 [P1-1修复] 加载旧格式映射: mapping_config_20250902_130727, 21个字段
2025-09-02 13:27:36.940 | INFO     | src.modules.data_import.config_sync_manager:load_mapping:483 | 🔧 [P1-1修复] 加载旧格式映射: mapping_config_20250902_130741, 21个字段
2025-09-02 13:27:36.947 | INFO     | src.modules.data_import.config_sync_manager:load_mapping:483 | 🔧 [P1-1修复] 加载旧格式映射: mapping_config_20250902_130750, 21个字段
2025-09-02 13:27:36.953 | INFO     | src.modules.data_import.config_sync_manager:load_mapping:483 | 🔧 [P1-1修复] 加载旧格式映射: mapping_config_20250902_130752, 21个字段
2025-09-02 13:27:36.958 | INFO     | src.modules.data_import.config_sync_manager:load_mapping:483 | 🔧 [P1-1修复] 加载旧格式映射: mapping_config_20250902_130757, 21个字段
2025-09-02 13:27:36.963 | INFO     | src.modules.data_import.config_sync_manager:load_mapping:483 | 🔧 [P1-1修复] 加载旧格式映射: mapping_config_20250902_130759, 21个字段
2025-09-02 13:27:36.966 | INFO     | src.modules.data_import.config_sync_manager:load_mapping:483 | 🔧 [P1-1修复] 加载旧格式映射: mapping_config_20250902_130803, 21个字段
2025-09-02 13:27:36.971 | INFO     | src.modules.data_import.config_sync_manager:load_mapping:483 | 🔧 [P1-1修复] 加载旧格式映射: mapping_config_20250902_130805, 21个字段
2025-09-02 13:27:36.976 | INFO     | src.modules.data_import.config_sync_manager:load_mapping:483 | 🔧 [P1-1修复] 加载旧格式映射: mapping_config_20250902_130811, 21个字段
2025-09-02 13:27:36.979 | INFO     | src.modules.data_import.config_sync_manager:load_mapping:483 | 🔧 [P1-1修复] 加载旧格式映射: mapping_config_20250902_130812, 21个字段
2025-09-02 13:27:36.982 | INFO     | src.modules.data_import.config_sync_manager:load_mapping:483 | 🔧 [P1-1修复] 加载旧格式映射: mapping_config_20250902_130817, 21个字段
2025-09-02 13:27:36.988 | INFO     | src.modules.data_import.config_sync_manager:load_mapping:483 | 🔧 [P1-1修复] 加载旧格式映射: mapping_config_20250902_130819, 21个字段
2025-09-02 13:27:36.990 | INFO     | src.modules.data_import.config_sync_manager:load_mapping:483 | 🔧 [P1-1修复] 加载旧格式映射: mapping_config_20250902_130852, 21个字段
2025-09-02 13:27:36.992 | INFO     | src.modules.data_import.config_sync_manager:load_mapping:483 | 🔧 [P1-1修复] 加载旧格式映射: mapping_config_20250902_130853, 21个字段
2025-09-02 13:27:36.998 | INFO     | src.modules.data_import.config_sync_manager:load_mapping:483 | 🔧 [P1-1修复] 加载旧格式映射: mapping_config_20250902_130906, 21个字段
2025-09-02 13:27:37.000 | INFO     | src.modules.data_import.config_sync_manager:load_mapping:483 | 🔧 [P1-1修复] 加载旧格式映射: mapping_config_20250902_131105, 21个字段
2025-09-02 13:27:37.004 | INFO     | src.modules.data_import.config_sync_manager:load_mapping:483 | 🔧 [P1-1修复] 加载旧格式映射: mapping_config_20250902_131111, 21个字段
2025-09-02 13:27:37.008 | INFO     | src.modules.data_import.config_sync_manager:load_mapping:483 | 🔧 [P1-1修复] 加载旧格式映射: mapping_config_20250902_131116, 21个字段
2025-09-02 13:27:37.013 | INFO     | src.modules.data_import.config_sync_manager:load_mapping:483 | 🔧 [P1-1修复] 加载旧格式映射: mapping_config_20250902_131118, 21个字段
2025-09-02 13:27:37.016 | INFO     | src.modules.data_import.config_sync_manager:load_mapping:483 | 🔧 [P1-1修复] 加载旧格式映射: mapping_config_20250902_131120, 21个字段
2025-09-02 13:27:37.018 | INFO     | src.modules.data_import.config_sync_manager:load_mapping:483 | 🔧 [P1-1修复] 加载旧格式映射: mapping_config_20250902_131123, 21个字段
2025-09-02 13:27:37.021 | INFO     | src.modules.data_import.config_sync_manager:load_mapping:483 | 🔧 [P1-1修复] 加载旧格式映射: mapping_config_20250902_131126, 21个字段
2025-09-02 13:27:37.025 | INFO     | src.modules.data_import.config_sync_manager:load_mapping:483 | 🔧 [P1-1修复] 加载旧格式映射: mapping_config_20250902_131132, 21个字段
2025-09-02 13:27:37.028 | INFO     | src.modules.data_import.config_sync_manager:load_mapping:483 | 🔧 [P1-1修复] 加载旧格式映射: mapping_config_20250902_131135, 21个字段
2025-09-02 13:27:37.030 | INFO     | src.modules.data_import.config_sync_manager:load_mapping:483 | 🔧 [P1-1修复] 加载旧格式映射: mapping_config_20250902_131154, 21个字段
2025-09-02 13:27:37.033 | INFO     | src.modules.data_import.config_sync_manager:load_mapping:483 | 🔧 [P1-1修复] 加载旧格式映射: mapping_config_20250902_131203, 21个字段
2025-09-02 13:27:37.042 | INFO     | src.modules.data_import.config_sync_manager:load_mapping:483 | 🔧 [P1-1修复] 加载旧格式映射: mapping_config_20250902_131206, 21个字段
2025-09-02 13:27:37.045 | INFO     | src.modules.data_import.config_sync_manager:load_mapping:483 | 🔧 [P1-1修复] 加载旧格式映射: mapping_config_20250902_131236, 21个字段
2025-09-02 13:27:37.048 | INFO     | src.modules.data_import.config_sync_manager:load_mapping:483 | 🔧 [P1-1修复] 加载旧格式映射: mapping_config_20250902_131242, 21个字段
2025-09-02 13:27:37.053 | INFO     | src.modules.data_import.config_sync_manager:load_mapping:483 | 🔧 [P1-1修复] 加载旧格式映射: mapping_config_20250902_131250, 21个字段
2025-09-02 13:27:37.055 | INFO     | src.modules.data_import.config_sync_manager:load_mapping:483 | 🔧 [P1-1修复] 加载旧格式映射: mapping_config_20250902_131253, 21个字段
2025-09-02 13:27:37.058 | INFO     | src.modules.data_import.config_sync_manager:load_mapping:483 | 🔧 [P1-1修复] 加载旧格式映射: mapping_config_20250902_131255, 21个字段
2025-09-02 13:27:37.061 | INFO     | src.modules.data_import.config_sync_manager:load_mapping:483 | 🔧 [P1-1修复] 加载旧格式映射: mapping_config_20250902_131257, 21个字段
2025-09-02 13:27:37.066 | INFO     | src.modules.data_import.config_sync_manager:load_mapping:483 | 🔧 [P1-1修复] 加载旧格式映射: mapping_config_20250902_131301, 21个字段
2025-09-02 13:27:37.068 | INFO     | src.modules.data_import.config_sync_manager:load_mapping:483 | 🔧 [P1-1修复] 加载旧格式映射: mapping_config_20250902_131303, 21个字段
2025-09-02 13:27:37.072 | INFO     | src.modules.data_import.config_sync_manager:load_mapping:483 | 🔧 [P1-1修复] 加载旧格式映射: mapping_config_20250902_131305, 21个字段
2025-09-02 13:27:37.077 | INFO     | src.modules.data_import.config_sync_manager:load_mapping:483 | 🔧 [P1-1修复] 加载旧格式映射: mapping_config_20250902_132423, 21个字段
2025-09-02 13:27:37.079 | INFO     | src.modules.data_import.config_sync_manager:load_mapping:483 | 🔧 [P1-1修复] 加载旧格式映射: mapping_config_20250902_132425, 21个字段
2025-09-02 13:27:37.082 | INFO     | src.modules.data_import.config_sync_manager:load_mapping:483 | 🔧 [P1-1修复] 加载旧格式映射: mapping_config_20250902_132428, 21个字段
2025-09-02 13:27:37.086 | INFO     | src.modules.data_import.config_sync_manager:load_mapping:483 | 🔧 [P1-1修复] 加载旧格式映射: mapping_config_20250902_132430, 21个字段
2025-09-02 13:27:37.089 | INFO     | src.modules.data_import.config_sync_manager:load_mapping:483 | 🔧 [P1-1修复] 加载旧格式映射: mapping_config_20250902_132434, 21个字段
2025-09-02 13:27:37.092 | INFO     | src.modules.data_import.config_sync_manager:load_mapping:483 | 🔧 [P1-1修复] 加载旧格式映射: mapping_config_20250902_132446, 21个字段
2025-09-02 13:27:37.096 | INFO     | src.modules.data_import.config_sync_manager:load_mapping:483 | 🔧 [P1-1修复] 加载旧格式映射: mapping_config_20250902_132450, 21个字段
2025-09-02 13:27:37.108 | INFO     | src.modules.data_import.config_sync_manager:load_mapping:483 | 🔧 [P1-1修复] 加载旧格式映射: mapping_config_20250902_132456, 21个字段
2025-09-02 13:27:37.112 | INFO     | src.modules.data_import.config_sync_manager:load_mapping:483 | 🔧 [P1-1修复] 加载旧格式映射: mapping_config_20250902_132500, 21个字段
2025-09-02 13:27:37.115 | INFO     | src.modules.data_import.config_sync_manager:load_mapping:483 | 🔧 [P1-1修复] 加载旧格式映射: mapping_config_20250902_132503, 21个字段
2025-09-02 13:27:37.120 | INFO     | src.modules.data_import.config_sync_manager:load_mapping:483 | 🔧 [P1-1修复] 加载旧格式映射: mapping_config_20250902_132505, 21个字段
2025-09-02 13:27:37.123 | INFO     | src.modules.data_import.config_sync_manager:load_mapping:483 | 🔧 [P1-1修复] 加载旧格式映射: mapping_config_20250902_132510, 21个字段
2025-09-02 13:27:37.127 | INFO     | src.modules.data_import.config_sync_manager:load_mapping:483 | 🔧 [P1-1修复] 加载旧格式映射: mapping_config_20250902_132511, 21个字段
2025-09-02 13:27:37.150 | INFO     | src.modules.data_import.config_sync_manager:load_mapping:483 | 🔧 [P1-1修复] 加载旧格式映射: mapping_config_20250902_132512, 21个字段
2025-09-02 13:27:37.154 | INFO     | src.modules.data_import.config_sync_manager:load_mapping:483 | 🔧 [P1-1修复] 加载旧格式映射: mapping_config_20250902_132531, 21个字段
2025-09-02 13:27:37.158 | INFO     | src.modules.data_import.config_sync_manager:load_mapping:483 | 🔧 [P1-1修复] 加载旧格式映射: mapping_config_20250902_132540, 21个字段
2025-09-02 13:27:37.164 | INFO     | src.modules.data_import.config_sync_manager:load_mapping:483 | 🔧 [P1-1修复] 加载旧格式映射: mapping_config_20250902_132543, 21个字段
2025-09-02 13:27:37.167 | INFO     | src.modules.data_import.config_sync_manager:load_mapping:483 | 🔧 [P1-1修复] 加载旧格式映射: mapping_config_20250902_132547, 21个字段
2025-09-02 13:27:37.171 | INFO     | src.modules.data_import.config_sync_manager:load_mapping:483 | 🔧 [P1-1修复] 加载旧格式映射: mapping_config_20250902_132549, 21个字段
2025-09-02 13:27:37.175 | INFO     | src.modules.data_import.config_sync_manager:load_mapping:483 | 🔧 [P1-1修复] 加载旧格式映射: mapping_config_20250902_132552, 21个字段
2025-09-02 13:27:37.178 | INFO     | src.modules.data_import.config_sync_manager:load_mapping:483 | 🔧 [P1-1修复] 加载旧格式映射: mapping_config_20250902_132554, 21个字段
2025-09-02 13:27:37.181 | INFO     | src.modules.data_import.config_sync_manager:load_mapping:483 | 🔧 [P1-1修复] 加载旧格式映射: mapping_config_20250902_132557, 21个字段
2025-09-02 13:27:37.181 | INFO     | src.gui.prototype.prototype_main_window:_load_field_mappings_from_file:6941 | ✅ [P1-2修复] 已加载字段映射信息，共102个表的映射
2025-09-02 13:27:37.191 | INFO     | src.gui.prototype.prototype_main_window:_get_default_table_name:2306 | 🔧 [兼容性修复] 暂无数据表，返回占位符（等待数据导入）
2025-09-02 13:27:37.192 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_set_data_impl:2736 | 🔧 [修复监控] 开始设置数据: 0行，表名: default_table
2025-09-02 13:27:37.193 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_validate_and_fix_data_consistency:4669 | 🔧 [数据修复] 数据或表头为空，跳过一致性验证
2025-09-02 13:27:37.194 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:auto_adjust_max_visible_rows:5573 | 数据量为0，不调整最大可见行数，保持当前值: 1000
2025-09-02 13:27:37.194 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_set_data_impl:2975 | 表格格式化完成: default_table, 类型: active_employees
2025-09-02 13:27:37.195 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_safe_set_column_count:7339 | 方案A：安全设置列数: 22
2025-09-02 13:27:37.196 | INFO     | src.gui.prototype.widgets.optimized_table_renderer:render_with_auto_strategy:252 | [数据流追踪] 自动渲染策略选择: 0行数据
2025-09-02 13:27:37.201 | INFO     | src.gui.prototype.widgets.optimized_table_renderer:render_with_auto_strategy:256 | [数据流追踪] 检测到空数据，使用快速清空策略
2025-09-02 13:27:37.201 | INFO     | src.gui.prototype.widgets.optimized_table_renderer:render_with_auto_strategy:264 | 🔧 [P0-修复] 空数据集设置表头: 22个
2025-09-02 13:27:37.202 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_update_visible_rows:466 | 🚨 [UI数据修复] _update_visible_rows调用，数据行数: 0
2025-09-02 13:27:37.203 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_set_data_impl:3159 | 表格数据设置完成: 0 行, 耗时: 9.8ms
2025-09-02 13:27:37.204 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_restore_sort_indicators_after_data_reload:8277 | 🔧 [P0-排序修复] 开始恢复排序指示器显示
2025-09-02 13:27:37.204 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_restore_sort_indicators_after_data_reload:8290 | 🔧 [P0-排序修复] 无排序状态需要恢复
2025-09-02 13:27:37.206 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:restore_column_widths:1752 | 🔧 [P2-3修复] 列宽已恢复: default_table (22/22 列, 表头数量: 22)
2025-09-02 13:27:37.207 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_set_data_impl:3238 | 🔧 [修复监控] UI强制刷新完成，数据可见
2025-09-02 13:27:37.207 | INFO     | src.gui.prototype.prototype_main_window:_show_empty_table_fallback:2331 | 🔧 [P0-修复] 使用备用方案显示了标准空表格，表头数量: 22个，所有表头为中文
2025-09-02 13:27:37.208 | INFO     | src.gui.widgets.pagination_widget:reset:584 | 分页状态已重置
2025-09-02 13:27:37.218 | INFO     | src.gui.prototype.prototype_main_window:_show_empty_table_with_prompt:8633 | 🔧 [数据流追踪] 无当前表名，使用默认在职人员表头: 22个字段
2025-09-02 13:27:37.222 | INFO     | src.gui.prototype.prototype_main_window:_get_default_table_name:2306 | 🔧 [兼容性修复] 暂无数据表，返回占位符（等待数据导入）
2025-09-02 13:27:37.224 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_set_data_impl:2736 | 🔧 [修复监控] 开始设置数据: 0行，表名: default_table
2025-09-02 13:27:37.228 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_validate_and_fix_data_consistency:4669 | 🔧 [数据修复] 数据或表头为空，跳过一致性验证
2025-09-02 13:27:37.229 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:auto_adjust_max_visible_rows:5573 | 数据量为0，不调整最大可见行数，保持当前值: 1000
2025-09-02 13:27:37.229 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_set_data_impl:2975 | 表格格式化完成: default_table, 类型: active_employees
2025-09-02 13:27:37.230 | INFO     | src.gui.prototype.widgets.optimized_table_renderer:render_with_auto_strategy:252 | [数据流追踪] 自动渲染策略选择: 0行数据
2025-09-02 13:27:37.231 | INFO     | src.gui.prototype.widgets.optimized_table_renderer:render_with_auto_strategy:256 | [数据流追踪] 检测到空数据，使用快速清空策略
2025-09-02 13:27:37.231 | INFO     | src.gui.prototype.widgets.optimized_table_renderer:render_with_auto_strategy:264 | 🔧 [P0-修复] 空数据集设置表头: 22个
2025-09-02 13:27:37.232 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_update_visible_rows:466 | 🚨 [UI数据修复] _update_visible_rows调用，数据行数: 0
2025-09-02 13:27:37.233 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_set_data_impl:3159 | 表格数据设置完成: 0 行, 耗时: 4.2ms
2025-09-02 13:27:37.233 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_restore_sort_indicators_after_data_reload:8277 | 🔧 [P0-排序修复] 开始恢复排序指示器显示
2025-09-02 13:27:37.234 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_restore_sort_indicators_after_data_reload:8290 | 🔧 [P0-排序修复] 无排序状态需要恢复
2025-09-02 13:27:37.235 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:restore_column_widths:1752 | 🔧 [P2-3修复] 列宽已恢复: default_table (22/22 列, 表头数量: 22)
2025-09-02 13:27:37.236 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_set_data_impl:3238 | 🔧 [修复监控] UI强制刷新完成，数据可见
2025-09-02 13:27:37.244 | INFO     | src.gui.prototype.prototype_main_window:_show_empty_table_fallback:2331 | 🔧 [P0-修复] 使用备用方案显示了标准空表格，表头数量: 22个，所有表头为中文
2025-09-02 13:27:37.245 | INFO     | src.gui.prototype.prototype_main_window:_show_empty_table_with_prompt:8651 | 已显示标准空表格，表头数量: 22
2025-09-02 13:27:37.245 | INFO     | src.gui.prototype.prototype_main_window:__init__:3704 | 原型主窗口初始化完成
2025-09-02 13:27:37.468 | INFO     | __main__:main:514 | 应用程序启动成功
2025-09-02 13:27:37.474 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_restore_ui_state_after_data_set:8312 | 🔧 [P0-2修复] 数据设置后UI状态恢复完成: default_table
2025-09-02 13:27:37.475 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:_delayed_auto_select_latest_data:1474 | 执行延迟的自动选择最新数据...
2025-09-02 13:27:37.475 | INFO     | src.modules.data_storage.dynamic_table_manager:get_latest_salary_data_path:2131 | 开始获取最新工资数据路径...
2025-09-02 13:27:37.478 | INFO     | src.modules.data_storage.dynamic_table_manager:get_table_list:1333 | 🔧 [深度修复] 找到 1 个匹配类型 'salary_data' 的表 (尝试 1/5)
2025-09-02 13:27:37.480 | WARNING  | src.modules.data_storage.dynamic_table_manager:get_latest_salary_data_path:2173 | 未找到有效的工资数据表
2025-09-02 13:27:37.481 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_restore_ui_state_after_data_set:8312 | 🔧 [P0-2修复] 数据设置后UI状态恢复完成: default_table
2025-09-02 13:27:37.482 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_restore_ui_state_after_data_set:8312 | 🔧 [P0-2修复] 数据设置后UI状态恢复完成: default_table
2025-09-02 13:27:37.483 | INFO     | src.gui.prototype.managers.responsive_layout_manager:_apply_layout:181 | 断点切换: sm (宽度: 1280px)
2025-09-02 13:27:37.488 | INFO     | src.gui.prototype.prototype_main_window:handle_responsive_change:2079 | MainWorkspaceArea 响应式适配: sm
2025-09-02 13:27:37.999 | INFO     | src.gui.prototype.prototype_main_window:delayed_heavy_fixes:9535 | UI亮度问题检测到并修复: ['table_opacity_in_stylesheet']
2025-09-02 13:27:37.999 | INFO     | src.gui.prototype.prototype_main_window:_fix_display_brightness_after_data_refresh:9445 | 🔧 [P1-2] 开始智能显示亮度修复（增强防护）
2025-09-02 13:27:38.003 | INFO     | src.gui.prototype.prototype_main_window:_fix_display_brightness_after_data_refresh:9459 | 🔧 [P0-1] 检测到亮度问题: ['table_opacity_in_stylesheet']
2025-09-02 13:27:38.004 | INFO     | src.gui.prototype.prototype_main_window:_apply_brightness_fixes:9993 | 🔧 [P1-2] 开始应用增强版亮度修复: ['table_opacity_in_stylesheet']
2025-09-02 13:27:38.022 | INFO     | src.gui.prototype.prototype_main_window:_fix_display_brightness_after_data_refresh:9465 | 🔧 [P0-1] 智能显示亮度修复完成
2025-09-02 13:27:38.481 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:auto_select_latest_data:1396 | 🔧 [P1-2修复] 开始自动选择最新数据...
2025-09-02 13:27:38.481 | INFO     | src.modules.data_storage.dynamic_table_manager:get_latest_salary_data_path:2131 | 开始获取最新工资数据路径...
2025-09-02 13:27:38.487 | INFO     | src.modules.data_storage.dynamic_table_manager:get_table_list:1333 | 🔧 [深度修复] 找到 1 个匹配类型 'salary_data' 的表 (尝试 1/5)
2025-09-02 13:27:38.487 | WARNING  | src.modules.data_storage.dynamic_table_manager:get_latest_salary_data_path:2173 | 未找到有效的工资数据表
2025-09-02 13:27:39.488 | INFO     | src.modules.data_storage.dynamic_table_manager:get_latest_salary_data_path:2131 | 开始获取最新工资数据路径...
2025-09-02 13:27:39.488 | INFO     | src.modules.data_storage.dynamic_table_manager:get_table_list:1333 | 🔧 [深度修复] 找到 1 个匹配类型 'salary_data' 的表 (尝试 1/5)
2025-09-02 13:27:39.488 | WARNING  | src.modules.data_storage.dynamic_table_manager:get_latest_salary_data_path:2173 | 未找到有效的工资数据表
2025-09-02 13:27:39.488 | WARNING  | src.gui.prototype.widgets.enhanced_navigation_panel:_get_latest_path_with_retry:1612 | 🔧 [P1-2修复] 3次重试已用尽，可能数据导入尚未完成
2025-09-02 13:28:40.060 | INFO     | src.gui.prototype.prototype_main_window:_on_import_data:654 | 数据导入功能被触发，发出 import_requested 信号。
2025-09-02 13:28:40.061 | INFO     | src.gui.prototype.prototype_main_window:_get_suggested_target_path:8456 | 检测到当前在工资表TAB，生成工资表默认路径
2025-09-02 13:28:40.064 | INFO     | src.gui.prototype.prototype_main_window:_on_import_data_requested:5906 | 接收到数据导入请求，推断的目标路径: 工资表 > 2025年 > 09月 > 全部在职人员。打开导入对话框。
2025-09-02 13:28:40 - src.gui.unified_data_import_window - INFO - 初始化统一数据导入窗口
2025-09-02 13:28:40.174 | INFO     | src.modules.system_config.config_manager:__init__:334 | 配置管理器初始化完成，配置文件: config.json
2025-09-02 13:28:40.178 | INFO     | src.modules.system_config.config_manager:load_config:361 | 正在加载配置文件: config.json
2025-09-02 13:28:40.178 | INFO     | src.modules.system_config.config_manager:_generate_validation_result:273 | 配置文件验证通过
2025-09-02 13:28:40.178 | INFO     | src.modules.system_config.config_manager:load_config:373 | 配置文件加载成功
2025-09-02 13:28:40.178 | INFO     | src.modules.data_storage.database_manager:_initialize_database:102 | 开始初始化数据库...
2025-09-02 13:28:40.187 | INFO     | src.modules.data_storage.database_manager:_initialize_database:157 | 数据库初始化完成
2025-09-02 13:28:40.187 | INFO     | src.modules.data_storage.database_manager:__init__:98 | 数据库管理器初始化完成，数据库路径: C:\test\salary_changes\salary_changes\data\db\salary_system.db
2025-09-02 13:28:40.187 | INFO     | src.modules.system_config.config_manager:__init__:334 | 配置管理器初始化完成，配置文件: config.json
2025-09-02 13:28:40.198 | INFO     | src.modules.data_storage.dynamic_table_manager:__init__:107 | 动态表管理器初始化完成
2025-09-02 13:28:40.198 | INFO     | src.modules.data_import.auto_field_mapping_generator:__init__:60 | 自动字段映射生成器初始化完成
2025-09-02 13:28:40.204 | INFO     | src.core.config_cache_manager:__init__:67 | 配置缓存管理器初始化完成: cache\config_cache, 最大条目数: 100
2025-09-02 13:28:40.205 | INFO     | src.modules.data_import.change_data_config_manager:__init__:69 | 配置缓存已启用
2025-09-02 13:28:40.209 | INFO     | src.modules.data_import.change_data_config_manager:__init__:75 | 配置管理器初始化完成，配置目录: C:\test\salary_changes\salary_changes\state\change_data_configs
2025-09-02 13:28:40.211 | INFO     | src.modules.data_import.multi_sheet_importer:__init__:78 | 多Sheet导入器初始化完成
2025-09-02 13:28:40 - src.gui.core.smart_mapping_engine - INFO - 成功加载历史配置: 3 个配置
2025-09-02 13:28:40 - src.gui.core.smart_mapping_engine - INFO - 智能映射引擎初始化完成
2025-09-02 13:28:40 - src.gui.core.template_manager - INFO - 模板管理器初始化完成: 内置模板 3 个, 用户模板 0 个
2025-09-02 13:28:40 - src.gui.core.validation_engine - INFO - 验证引擎初始化完成
2025-09-02 13:28:40.278 | INFO     | src.modules.data_import.sheet_config_manager:__init__:128 | Sheet配置管理器初始化完成，配置目录: state\sheet_configs
2025-09-02 13:28:40 - src.gui.unified_data_import_window - INFO - 统一导入管理器初始化完成（包含第二阶段功能）
2025-09-02 13:28:40.287 | INFO     | src.modules.system_config.config_manager:__init__:334 | 配置管理器初始化完成，配置文件: config.json
2025-09-02 13:28:40 - src.gui.unified_data_import_window - INFO - 核心组件初始化成功
2025-09-02 13:28:40 - src.gui.unified_data_import_window - INFO - 接收到高级配置变化: {'file_import': {'default_import_path': 'C:\\test\\salary_changes\\salary_changes\\data\\工资表\\2025年5月份正式工工资（报财务)  最终版.xls', 'supported_formats': ['xlsx', 'xls', 'csv'], 'max_file_size_mb': 100, 'auto_detect_encoding': True, 'sheet_selection_strategy': 'all', 'data_start_row': 2, 'header_row': 1, 'skip_rows': 0, 'skip_footer_rows': 0, 'auto_detect_header': True, 'ignore_empty_rows': True}, 'field_mapping': {'mapping_algorithm': 'fuzzy_match', 'similarity_threshold': 80, 'auto_mapping_enabled': True, 'required_field_check': True, 'field_type_validation': True, 'save_mapping_history': True}, 'smart_recommendations': {'confidence_threshold': 70, 'enable_history_learning': True, 'enable_semantic_analysis': True, 'auto_apply_high_confidence': False, 'template_priority': 0, 'max_saved_templates': 50}, 'data_processing': {'strict_validation': False, 'null_value_strategy': 0, 'auto_type_conversion': True, 'duplicate_strategy': 0, 'batch_size': 1000, 'error_tolerance': 10, 'remove_duplicates': False, 'handle_missing_values': 'keep', 'format_numbers': True, 'format_dates': True, 'trim_whitespace': True, 'convert_data_types': True, 'data_validation': True, 'custom_rules': [], 'saved_templates': []}, 'ui_customization': {'table_row_limit': 200, 'show_detailed_logs': False, 'show_confidence_indicators': True, 'auto_save_interval': 5, 'show_confirmation_dialogs': True, 'show_shortcuts': True}, 'performance': {'max_memory_usage': 2048, 'enable_caching': True, 'preload_data': False, 'thread_count': 4, 'enable_async_processing': True, 'progress_update_frequency': 100}}
2025-09-02 13:28:40 - src.gui.unified_data_import_window - INFO - 应用默认导入文件: C:\test\salary_changes\salary_changes\data\工资表\2025年5月份正式工工资（报财务)  最终版.xls
2025-09-02 13:28:40 - src.gui.unified_data_import_window - INFO - UI未初始化，保存默认路径供后续应用: C:\test\salary_changes\salary_changes\data\工资表\2025年5月份正式工工资（报财务)  最终版.xls
2025-09-02 13:28:40 - src.gui.unified_data_import_window - INFO - 文件导入配置已应用，包括Excel表结构设置: {'data_start_row': 2, 'header_row': 1, 'skip_rows': 0, 'skip_footer_rows': 0, 'auto_detect_header': True, 'ignore_empty_rows': True}
2025-09-02 13:28:40 - src.gui.unified_data_import_window - INFO - 字段映射配置已应用
2025-09-02 13:28:40 - src.gui.unified_data_import_window - INFO - 智能推荐配置已应用
2025-09-02 13:28:40 - src.gui.unified_data_import_window - INFO - 数据处理配置已应用
2025-09-02 13:28:40 - src.gui.unified_data_import_window - INFO - 界面个性化配置已应用
2025-09-02 13:28:40 - src.gui.unified_data_import_window - INFO - 性能优化配置已应用
2025-09-02 13:28:40 - src.gui.unified_data_import_window - INFO - 所有高级配置域已成功应用: ['file_import', 'field_mapping', 'smart_recommendations', 'data_processing', 'ui_customization', 'performance']
2025-09-02 13:28:40 - src.gui.unified_data_import_window - INFO - 🔧 [P0修复] 已加载保存的高级配置
2025-09-02 13:28:40 - src.gui.unified_data_import_window - INFO - 工作线程初始化完成
2025-09-02 13:28:40.303 | INFO     | src.modules.data_import.sheet_config_manager:__init__:128 | Sheet配置管理器初始化完成，配置目录: state\sheet_configs
2025-09-02 13:28:40 - src.gui.core.smart_mapping_engine - INFO - 成功加载历史配置: 3 个配置
2025-09-02 13:28:40 - src.gui.core.smart_mapping_engine - INFO - 智能映射引擎初始化完成
2025-09-02 13:28:40 - src.gui.core.template_manager - INFO - 模板管理器初始化完成: 内置模板 3 个, 用户模板 0 个
2025-09-02 13:28:40 - src.gui.core.validation_engine - INFO - 验证引擎初始化完成
2025-09-02 13:28:40 - src.gui.performance_optimizer - INFO - 性能优化器初始化完成
2025-09-02 13:28:40.338 | INFO     | src.modules.data_import.field_type_manager:__init__:41 | 字段类型管理器初始化完成，存储目录: C:\test\salary_changes\salary_changes\state\field_types
2025-09-02 13:28:40 - src.gui.unified_data_import_window - WARNING - 初始化ConfigSyncManager失败: ArchitectureFactory.__init__() missing 2 required positional arguments: 'db_manager' and 'config_manager'
2025-09-02 13:28:40.350 | INFO     | src.modules.data_import.config_sync_manager:_do_config_initialization:153 | 使用已存在的配置文件: state/data/field_mappings.json
2025-09-02 13:28:40.355 | INFO     | src.modules.data_import.config_sync_manager:__init__:81 | 🆕 [新架构] 配置同步管理器初始化完成（依赖注入）
2025-09-02 13:28:40 - src.gui.unified_data_import_window - INFO - 创建备用ConfigSyncManager实例
2025-09-02 13:28:40.469 | INFO     | src.modules.data_import.field_type_manager:__init__:41 | 字段类型管理器初始化完成，存储目录: C:\test\salary_changes\salary_changes\state\field_types
2025-09-02 13:28:40.469 | INFO     | src.modules.data_import.formatting_engine:register_rule_type:480 | 注册规则类型: number
2025-09-02 13:28:40.473 | INFO     | src.modules.data_import.formatting_engine:register_rule_type:480 | 注册规则类型: string
2025-09-02 13:28:40.473 | INFO     | src.modules.data_import.formatting_engine:register_rule_type:480 | 注册规则类型: date
2025-09-02 13:28:40.475 | INFO     | src.modules.data_import.formatting_engine:register_rule_type:480 | 注册规则类型: code
2025-09-02 13:28:40.475 | INFO     | src.modules.data_import.formatting_engine:register_rule_type:480 | 注册规则类型: custom
2025-09-02 13:28:40.476 | INFO     | src.modules.data_import.formatting_engine:register_field_type:491 | 注册字段类型: salary_float - 工资金额
2025-09-02 13:28:40.476 | INFO     | src.modules.data_import.formatting_engine:register_field_type:491 | 注册字段类型: employee_id_string - 工号
2025-09-02 13:28:40.478 | INFO     | src.modules.data_import.formatting_engine:register_field_type:491 | 注册字段类型: name_string - 姓名
2025-09-02 13:28:40.478 | INFO     | src.modules.data_import.formatting_engine:register_field_type:491 | 注册字段类型: date_string - 日期
2025-09-02 13:28:40.479 | INFO     | src.modules.data_import.formatting_engine:register_field_type:491 | 注册字段类型: id_number_string - 身份证号
2025-09-02 13:28:40.479 | INFO     | src.modules.data_import.formatting_engine:register_field_type:491 | 注册字段类型: code_string - 代码
2025-09-02 13:28:40.479 | INFO     | src.modules.data_import.formatting_engine:register_field_type:491 | 注册字段类型: float - 浮点数
2025-09-02 13:28:40.479 | INFO     | src.modules.data_import.formatting_engine:register_field_type:491 | 注册字段类型: integer - 整数
2025-09-02 13:28:40.479 | INFO     | src.modules.data_import.formatting_engine:register_field_type:491 | 注册字段类型: text_string - 文本字符串
2025-09-02 13:28:40.479 | INFO     | src.modules.data_import.formatting_engine:register_field_type:491 | 注册字段类型: personnel_category_code - 人员类别代码
2025-09-02 13:28:40 - src.gui.widgets.field_type_config_widget - INFO - 已加载 10 个内置类型和 0 个自定义类型
2025-09-02 13:28:40 - src.gui.widgets.field_type_config_widget - INFO - 字段类型配置组件初始化完成
2025-09-02 13:28:40 - src.gui.unified_data_import_window - INFO - 响应式分割器设置完成: 左侧=308px, 右侧=1092px
2025-09-02 13:28:40 - src.gui.unified_data_import_window - INFO - UI界面创建完成
2025-09-02 13:28:40 - src.gui.unified_data_import_window - INFO - 应用待处理的默认文件: C:\test\salary_changes\salary_changes\data\工资表\2025年5月份正式工工资（报财务)  最终版.xls
2025-09-02 13:28:40.811 | INFO     | src.modules.data_import.excel_importer:get_sheet_names:178 | 检测到工作表: ['离休人员工资表', '退休人员工资表', '全部在职人员工资表', 'A岗职工']
2025-09-02 13:28:40.812 | INFO     | src.modules.data_import.excel_importer:validate_file:115 | 文件验证成功: 2025年5月份正式工工资（报财务)  最终版.xls (1.17MB)
2025-09-02 13:28:40.815 | INFO     | src.modules.data_import.excel_importer:import_data:274 | 开始导入Excel文件: 2025年5月份正式工工资（报财务)  最终版.xls
2025-09-02 13:28:40.816 | INFO     | src.utils.log_config:log_file_operation:336 | 文件C:\test\salary_changes\salary_changes\data\工资表\2025年5月份正式工工资（报财务)  最终版.xls: read
2025-09-02 13:28:40.924 | INFO     | src.modules.data_import.excel_importer:_import_normal_file:337 | [修复标识] Excel读取完成: 16列 (列过滤: 否)
2025-09-02 13:28:40.932 | INFO     | src.modules.data_import.excel_importer:_clean_data:602 | [修复标识] Excel导入器 _clean_data 方法 - 保留所有列结构修复已执行
2025-09-02 13:28:40.934 | INFO     | src.modules.data_import.excel_importer:_clean_data:613 | 数据清理完成: 保留所有 16 列 (原始 16 列)
2025-09-02 13:28:40.935 | INFO     | src.modules.data_import.excel_importer:_import_normal_file:362 | 导入完成: 3行 x 16列
2025-09-02 13:28:40.938 | INFO     | src.modules.data_import.excel_importer:_filter_invalid_data:742 | 数据质量检查: 已过滤1条姓名为空的记录
2025-09-02 13:28:40.942 | INFO     | src.modules.data_import.excel_importer:_filter_invalid_data:785 | 数据过滤完成: 原始3条记录，过滤1条无效记录，有效记录2条
2025-09-02 13:28:40.943 | INFO     | src.modules.data_import.excel_importer:import_data:301 | [修复标识] 数据导入最终完成: 2行 × 16列
2025-09-02 13:28:40.944 | INFO     | src.utils.log_config:log_file_operation:336 | 文件C:\test\salary_changes\salary_changes\data\工资表\2025年5月份正式工工资（报财务)  最终版.xls: read
2025-09-02 13:28:41.049 | INFO     | src.modules.data_import.excel_importer:_clean_data:602 | [修复标识] Excel导入器 _clean_data 方法 - 保留所有列结构修复已执行
2025-09-02 13:28:41.052 | INFO     | src.modules.data_import.excel_importer:_clean_data:613 | 数据清理完成: 保留所有 27 列 (原始 27 列)
2025-09-02 13:28:41.055 | INFO     | src.utils.log_config:log_file_operation:336 | 文件C:\test\salary_changes\salary_changes\data\工资表\2025年5月份正式工工资（报财务)  最终版.xls: read
2025-09-02 13:28:41.164 | INFO     | src.modules.data_import.excel_importer:_clean_data:602 | [修复标识] Excel导入器 _clean_data 方法 - 保留所有列结构修复已执行
2025-09-02 13:28:41.166 | INFO     | src.modules.data_import.excel_importer:_clean_data:613 | 数据清理完成: 保留所有 23 列 (原始 23 列)
2025-09-02 13:28:41.170 | INFO     | src.utils.log_config:log_file_operation:336 | 文件C:\test\salary_changes\salary_changes\data\工资表\2025年5月份正式工工资（报财务)  最终版.xls: read
2025-09-02 13:28:41.285 | INFO     | src.modules.data_import.excel_importer:_clean_data:602 | [修复标识] Excel导入器 _clean_data 方法 - 保留所有列结构修复已执行
2025-09-02 13:28:41.287 | INFO     | src.modules.data_import.excel_importer:_clean_data:613 | 数据清理完成: 保留所有 21 列 (原始 21 列)
2025-09-02 13:28:41 - src.gui.unified_data_import_window - INFO - 已加载 4 个Sheet
2025-09-02 13:28:41 - src.gui.unified_data_import_window - INFO - 成功加载 4 个工作表
2025-09-02 13:28:41 - src.gui.unified_data_import_window - INFO - 待处理配置应用完成: C:\test\salary_changes\salary_changes\data\工资表\2025年5月份正式工工资（报财务)  最终版.xls
2025-09-02 13:28:41 - src.gui.unified_data_import_window - INFO - 信号连接完成
2025-09-02 13:28:41 - src.gui.unified_data_import_window - INFO - 统一数据导入窗口初始化完成
2025-09-02 13:28:41 - src.gui.unified_data_import_window - INFO - 响应式分割器设置完成: 左侧=308px, 右侧=1092px
2025-09-02 13:28:41 - src.gui.unified_data_import_window - INFO - 响应式布局初始化完成
2025-09-02 13:28:41 - src.gui.unified_data_import_window - INFO - 响应式分割器设置完成: 左侧=308px, 右侧=1092px
2025-09-02 13:28:43 - src.gui.unified_data_import_window - INFO - 开始打开高级配置对话框...
2025-09-02 13:28:43 - src.gui.unified_data_import_window - INFO - AdvancedConfigDialog 导入成功，开始初始化...
2025-09-02 13:28:43 - src.gui.advanced_config_dialog - INFO - 高级配置对话框开始初始化...
2025-09-02 13:28:43 - src.gui.advanced_config_dialog - INFO - 开始初始化UI...
2025-09-02 13:28:43 - src.gui.advanced_config_dialog - INFO - 开始加载配置...
2025-09-02 13:28:43 - src.gui.advanced_config_dialog - INFO - 高级配置加载成功
2025-09-02 13:28:43 - src.gui.advanced_config_dialog - INFO - 开始连接信号...
2025-09-02 13:28:43 - src.gui.advanced_config_dialog - INFO - 高级配置对话框初始化完成
2025-09-02 13:28:43 - src.gui.unified_data_import_window - INFO - AdvancedConfigDialog 初始化完成
2025-09-02 13:28:43 - src.gui.unified_data_import_window - INFO - config_changed 信号连接成功
2025-09-02 13:28:43 - src.gui.unified_data_import_window - INFO - 准备显示高级配置对话框...
2025-09-02 13:28:45 - src.gui.unified_data_import_window - INFO - 高级配置对话框关闭，返回值: 0
2025-09-02 13:28:46 - src.gui.unified_data_import_window - INFO - 选中Sheet: A岗职工
2025-09-02 13:28:46.487 | INFO     | src.modules.data_import.sheet_config_manager:get_or_create_config:161 | 为Sheet 'A岗职工' 创建默认配置
2025-09-02 13:28:46 - src.gui.unified_data_import_window - INFO - 当前Sheet变化: A岗职工
2025-09-02 13:28:46.494 | INFO     | src.modules.data_import.sheet_config_manager:get_or_create_config:161 | 为Sheet 'A岗职工' 创建默认配置
2025-09-02 13:28:46.519 | INFO     | src.modules.data_import.config_template_manager:_load_templates:579 | 加载了 10 个模板
2025-09-02 13:28:46.521 | INFO     | src.modules.data_import.config_template_manager:_init_builtin_templates:184 | 初始化了 4 个内置模板
2025-09-02 13:28:46.521 | INFO     | src.modules.data_import.config_template_manager:__init__:100 | 配置模板管理器初始化完成，模板目录: state\config_templates
2025-09-02 13:28:46.523 | INFO     | src.gui.widgets.data_processing_widget:_load_template_list:732 | 加载了 10 个模板
2025-09-02 13:28:46.523 | INFO     | src.gui.widgets.data_processing_widget:update_for_sheet:416 | 数据处理配置已切换到Sheet: A岗职工
2025-09-02 13:28:46 - src.gui.unified_data_import_window - INFO - 更新字段映射配置: A岗职工
2025-09-02 13:28:46.526 | INFO     | src.modules.data_import.excel_importer:validate_file:115 | 文件验证成功: 2025年5月份正式工工资（报财务)  最终版.xls (1.17MB)
2025-09-02 13:28:46.527 | INFO     | src.modules.data_import.excel_importer:import_data:274 | 开始导入Excel文件: 2025年5月份正式工工资（报财务)  最终版.xls
2025-09-02 13:28:46.527 | INFO     | src.utils.log_config:log_file_operation:336 | 文件C:\test\salary_changes\salary_changes\data\工资表\2025年5月份正式工工资（报财务)  最终版.xls: read
2025-09-02 13:28:46.628 | INFO     | src.modules.data_import.excel_importer:_import_normal_file:337 | [修复标识] Excel读取完成: 21列 (列过滤: 否)
2025-09-02 13:28:46.637 | INFO     | src.modules.data_import.excel_importer:_clean_data:602 | [修复标识] Excel导入器 _clean_data 方法 - 保留所有列结构修复已执行
2025-09-02 13:28:46.637 | INFO     | src.modules.data_import.excel_importer:_clean_data:613 | 数据清理完成: 保留所有 21 列 (原始 21 列)
2025-09-02 13:28:46.637 | INFO     | src.modules.data_import.excel_importer:_import_normal_file:362 | 导入完成: 1行 x 21列
2025-09-02 13:28:46.645 | INFO     | src.modules.data_import.excel_importer:import_data:301 | [修复标识] 数据导入最终完成: 1行 × 21列
2025-09-02 13:28:46 - src.gui.unified_data_import_window - INFO - 从Excel文件获取字段: 21 个
2025-09-02 13:28:46 - src.gui.unified_data_import_window - INFO - 加载Excel字段: 21 个字段, 表类型: 
2025-09-02 13:28:46 - src.gui.unified_data_import_window - INFO - 字段映射表格创建完成: 21 行
2025-09-02 13:28:46 - src.gui.unified_data_import_window - INFO - 更新预览验证内容: A岗职工
2025-09-02 13:28:46.799 | INFO     | src.utils.log_config:log_file_operation:336 | 文件C:\test\salary_changes\salary_changes\data\工资表\2025年5月份正式工工资（报财务)  最终版.xls: read
2025-09-02 13:28:46.919 | INFO     | src.modules.data_import.excel_importer:_clean_data:602 | [修复标识] Excel导入器 _clean_data 方法 - 保留所有列结构修复已执行
2025-09-02 13:28:46.925 | INFO     | src.modules.data_import.excel_importer:_clean_data:613 | 数据清理完成: 保留所有 21 列 (原始 21 列)
2025-09-02 13:28:46.928 | INFO     | src.modules.data_import.excel_importer:_filter_invalid_data:742 | 数据质量检查: 已过滤1条姓名为空的记录
2025-09-02 13:28:46.928 | INFO     | src.modules.data_import.excel_importer:_filter_invalid_data:785 | 数据过滤完成: 原始63条记录，过滤1条无效记录，有效记录62条
2025-09-02 13:28:46 - src.gui.unified_data_import_window - INFO - 预览数据加载完成（已应用格式化）: A岗职工 - 62 行
2025-09-02 13:28:46 - src.gui.unified_data_import_window - INFO - 成功更新预览数据: 62 行, 21 列
2025-09-02 13:28:46 - src.gui.unified_data_import_window - INFO - 已加载Sheet 'A岗职工' 的预览数据: 62 行
2025-09-02 13:28:49 - src.gui.unified_data_import_window - INFO - 选项卡切换到: 👁️ 预览验证 (索引: 2)
2025-09-02 13:28:49.237 | INFO     | src.modules.data_import.config_sync_manager:save_mapping:447 | 字段映射保存成功: mapping_config_20250902_132849
2025-09-02 13:28:49 - src.gui.unified_data_import_window - INFO - 字段映射实时保存成功: mapping_config_20250902_132849
2025-09-02 13:28:49.237 | WARNING  | src.modules.data_import.formatting_engine:apply:93 | 数值格式化失败: could not convert string to float: '柳红胜'
2025-09-02 13:28:49.237 | WARNING  | src.modules.data_import.formatting_engine:apply:93 | 数值格式化失败: could not convert string to float: '自动化学院'
2025-09-02 13:28:49.237 | WARNING  | src.modules.data_import.formatting_engine:apply:93 | 数值格式化失败: could not convert string to float: '科研单位人员'
2025-09-02 13:28:49.237 | WARNING  | src.modules.data_import.formatting_engine:apply:93 | 数值格式化失败: could not convert string to float: '孙玲'
2025-09-02 13:28:49.237 | WARNING  | src.modules.data_import.formatting_engine:apply:93 | 数值格式化失败: could not convert string to float: '自动化学院'
2025-09-02 13:28:49.237 | WARNING  | src.modules.data_import.formatting_engine:apply:93 | 数值格式化失败: could not convert string to float: '教学院其它人员'
2025-09-02 13:28:49.237 | WARNING  | src.modules.data_import.formatting_engine:apply:93 | 数值格式化失败: could not convert string to float: '王祺景'
2025-09-02 13:28:49.244 | WARNING  | src.modules.data_import.formatting_engine:apply:93 | 数值格式化失败: could not convert string to float: '音乐学院'
2025-09-02 13:28:49.244 | WARNING  | src.modules.data_import.formatting_engine:apply:93 | 数值格式化失败: could not convert string to float: '教学院其它人员'
2025-09-02 13:28:49.245 | WARNING  | src.modules.data_import.formatting_engine:apply:93 | 数值格式化失败: could not convert string to float: '方艳'
2025-09-02 13:28:49.245 | WARNING  | src.modules.data_import.formatting_engine:apply:93 | 数值格式化失败: could not convert string to float: '艺术与设计学院'
2025-09-02 13:28:49.245 | WARNING  | src.modules.data_import.formatting_engine:apply:93 | 数值格式化失败: could not convert string to float: '教学单位专技人员'
2025-09-02 13:28:49.245 | WARNING  | src.modules.data_import.formatting_engine:apply:93 | 数值格式化失败: could not convert string to float: '刘蕤'
2025-09-02 13:28:49.248 | WARNING  | src.modules.data_import.formatting_engine:apply:93 | 数值格式化失败: could not convert string to float: '艺术与设计学院'
2025-09-02 13:28:49.249 | WARNING  | src.modules.data_import.formatting_engine:apply:93 | 数值格式化失败: could not convert string to float: '教学单位专技人员'
2025-09-02 13:28:49.249 | WARNING  | src.modules.data_import.formatting_engine:apply:93 | 数值格式化失败: could not convert string to float: '盛金锋'
2025-09-02 13:28:49.251 | WARNING  | src.modules.data_import.formatting_engine:apply:93 | 数值格式化失败: could not convert string to float: '信息中心'
2025-09-02 13:28:49.251 | WARNING  | src.modules.data_import.formatting_engine:apply:93 | 数值格式化失败: could not convert string to float: '管理单位人员'
2025-09-02 13:28:49.251 | WARNING  | src.modules.data_import.formatting_engine:apply:93 | 数值格式化失败: could not convert string to float: '陈姣梅'
2025-09-02 13:28:49.253 | WARNING  | src.modules.data_import.formatting_engine:apply:93 | 数值格式化失败: could not convert string to float: '校医院'
2025-09-02 13:28:49.266 | WARNING  | src.modules.data_import.formatting_engine:apply:93 | 数值格式化失败: could not convert string to float: '管理单位人员'
2025-09-02 13:28:49.266 | WARNING  | src.modules.data_import.formatting_engine:apply:93 | 数值格式化失败: could not convert string to float: '刘孟君'
2025-09-02 13:28:49.269 | WARNING  | src.modules.data_import.formatting_engine:apply:93 | 数值格式化失败: could not convert string to float: '校工会、妇联'
2025-09-02 13:28:49.269 | WARNING  | src.modules.data_import.formatting_engine:apply:93 | 数值格式化失败: could not convert string to float: '管理单位人员'
2025-09-02 13:28:49.274 | WARNING  | src.modules.data_import.formatting_engine:apply:93 | 数值格式化失败: could not convert string to float: '杨美玲'
2025-09-02 13:28:49.274 | WARNING  | src.modules.data_import.formatting_engine:apply:93 | 数值格式化失败: could not convert string to float: '图书馆'
2025-09-02 13:28:49.274 | WARNING  | src.modules.data_import.formatting_engine:apply:93 | 数值格式化失败: could not convert string to float: '管理单位人员'
2025-09-02 13:28:49.277 | WARNING  | src.modules.data_import.formatting_engine:apply:93 | 数值格式化失败: could not convert string to float: '陈仲华'
2025-09-02 13:28:49.277 | WARNING  | src.modules.data_import.formatting_engine:apply:93 | 数值格式化失败: could not convert string to float: '图书馆'
2025-09-02 13:28:49.277 | WARNING  | src.modules.data_import.formatting_engine:apply:93 | 数值格式化失败: could not convert string to float: '管理单位人员'
2025-09-02 13:28:49.280 | WARNING  | src.modules.data_import.formatting_engine:apply:93 | 数值格式化失败: could not convert string to float: '夏端辉'
2025-09-02 13:28:49.280 | WARNING  | src.modules.data_import.formatting_engine:apply:93 | 数值格式化失败: could not convert string to float: '图书馆'
2025-09-02 13:28:49.280 | WARNING  | src.modules.data_import.formatting_engine:apply:93 | 数值格式化失败: could not convert string to float: '管理单位人员'
2025-09-02 13:28:49.280 | WARNING  | src.modules.data_import.formatting_engine:apply:93 | 数值格式化失败: could not convert string to float: '徐颖霞'
2025-09-02 13:28:49.280 | WARNING  | src.modules.data_import.formatting_engine:apply:93 | 数值格式化失败: could not convert string to float: '图书馆'
2025-09-02 13:28:49.280 | WARNING  | src.modules.data_import.formatting_engine:apply:93 | 数值格式化失败: could not convert string to float: '管理单位人员'
2025-09-02 13:28:49.280 | WARNING  | src.modules.data_import.formatting_engine:apply:93 | 数值格式化失败: could not convert string to float: '孙刘萍'
2025-09-02 13:28:49.303 | WARNING  | src.modules.data_import.formatting_engine:apply:93 | 数值格式化失败: could not convert string to float: '数学与统计学院'
2025-09-02 13:28:49.305 | WARNING  | src.modules.data_import.formatting_engine:apply:93 | 数值格式化失败: could not convert string to float: '教学院其它人员'
2025-09-02 13:28:49.305 | WARNING  | src.modules.data_import.formatting_engine:apply:93 | 数值格式化失败: could not convert string to float: '周俊'
2025-09-02 13:28:49.305 | WARNING  | src.modules.data_import.formatting_engine:apply:93 | 数值格式化失败: could not convert string to float: '社会合作与校友工作处'
2025-09-02 13:28:49.309 | WARNING  | src.modules.data_import.formatting_engine:apply:93 | 数值格式化失败: could not convert string to float: '管理单位人员'
2025-09-02 13:28:49.309 | WARNING  | src.modules.data_import.formatting_engine:apply:93 | 数值格式化失败: could not convert string to float: '赵晓辰'
2025-09-02 13:28:49.310 | WARNING  | src.modules.data_import.formatting_engine:apply:93 | 数值格式化失败: could not convert string to float: '临床医学院'
2025-09-02 13:28:49.311 | WARNING  | src.modules.data_import.formatting_engine:apply:93 | 数值格式化失败: could not convert string to float: '教学院其它人员'
2025-09-02 13:28:49.312 | WARNING  | src.modules.data_import.formatting_engine:apply:93 | 数值格式化失败: could not convert string to float: '梅秀英'
2025-09-02 13:28:49.312 | WARNING  | src.modules.data_import.formatting_engine:apply:93 | 数值格式化失败: could not convert string to float: '临床医学院'
2025-09-02 13:28:49.314 | WARNING  | src.modules.data_import.formatting_engine:apply:93 | 数值格式化失败: could not convert string to float: '教学院其它人员'
2025-09-02 13:28:49.314 | WARNING  | src.modules.data_import.formatting_engine:apply:93 | 数值格式化失败: could not convert string to float: '李淑华'
2025-09-02 13:28:49.314 | WARNING  | src.modules.data_import.formatting_engine:apply:93 | 数值格式化失败: could not convert string to float: '口腔与眼视光医学院'
2025-09-02 13:28:49.314 | WARNING  | src.modules.data_import.formatting_engine:apply:93 | 数值格式化失败: could not convert string to float: '教学院其它人员'
2025-09-02 13:28:49.316 | WARNING  | src.modules.data_import.formatting_engine:apply:93 | 数值格式化失败: could not convert string to float: '杜艳'
2025-09-02 13:28:49.316 | WARNING  | src.modules.data_import.formatting_engine:apply:93 | 数值格式化失败: could not convert string to float: '科学技术发展院、科协'
2025-09-02 13:28:49.316 | WARNING  | src.modules.data_import.formatting_engine:apply:93 | 数值格式化失败: could not convert string to float: '管理单位人员'
2025-09-02 13:28:49.319 | WARNING  | src.modules.data_import.formatting_engine:apply:93 | 数值格式化失败: could not convert string to float: '刘昕芫'
2025-09-02 13:28:49.338 | WARNING  | src.modules.data_import.formatting_engine:apply:93 | 数值格式化失败: could not convert string to float: '经济与管理学院'
2025-09-02 13:28:49.338 | WARNING  | src.modules.data_import.formatting_engine:apply:93 | 数值格式化失败: could not convert string to float: '教学院其它人员'
2025-09-02 13:28:49.338 | WARNING  | src.modules.data_import.formatting_engine:apply:93 | 数值格式化失败: could not convert string to float: '林海'
2025-09-02 13:28:49.338 | WARNING  | src.modules.data_import.formatting_engine:apply:93 | 数值格式化失败: could not convert string to float: '经济与管理学院'
2025-09-02 13:28:49.338 | WARNING  | src.modules.data_import.formatting_engine:apply:93 | 数值格式化失败: could not convert string to float: '教学单位专技人员'
2025-09-02 13:28:49.338 | WARNING  | src.modules.data_import.formatting_engine:apply:93 | 数值格式化失败: could not convert string to float: '石波'
2025-09-02 13:28:49.338 | WARNING  | src.modules.data_import.formatting_engine:apply:93 | 数值格式化失败: could not convert string to float: '继续教育学院、职业技能培训鉴定中心'
2025-09-02 13:28:49.344 | WARNING  | src.modules.data_import.formatting_engine:apply:93 | 数值格式化失败: could not convert string to float: '管理单位人员'
2025-09-02 13:28:49.344 | WARNING  | src.modules.data_import.formatting_engine:apply:93 | 数值格式化失败: could not convert string to float: '言帆'
2025-09-02 13:28:49.344 | WARNING  | src.modules.data_import.formatting_engine:apply:93 | 数值格式化失败: could not convert string to float: '继续教育学院、职业技能培训鉴定中心'
2025-09-02 13:28:49.344 | WARNING  | src.modules.data_import.formatting_engine:apply:93 | 数值格式化失败: could not convert string to float: '管理单位人员'
2025-09-02 13:28:49.344 | WARNING  | src.modules.data_import.formatting_engine:apply:93 | 数值格式化失败: could not convert string to float: '夏春梅'
2025-09-02 13:28:49.348 | WARNING  | src.modules.data_import.formatting_engine:apply:93 | 数值格式化失败: could not convert string to float: '计算机科学与技术学院'
2025-09-02 13:28:49.348 | WARNING  | src.modules.data_import.formatting_engine:apply:93 | 数值格式化失败: could not convert string to float: '教学院其它人员'
2025-09-02 13:28:49.348 | WARNING  | src.modules.data_import.formatting_engine:apply:93 | 数值格式化失败: could not convert string to float: '肖文文'
2025-09-02 13:28:49.351 | WARNING  | src.modules.data_import.formatting_engine:apply:93 | 数值格式化失败: could not convert string to float: '计算机科学与技术学院'
2025-09-02 13:28:49.376 | WARNING  | src.modules.data_import.formatting_engine:apply:93 | 数值格式化失败: could not convert string to float: '教学院其它人员'
2025-09-02 13:28:49.378 | WARNING  | src.modules.data_import.formatting_engine:apply:93 | 数值格式化失败: could not convert string to float: '黄志成'
2025-09-02 13:28:49.378 | WARNING  | src.modules.data_import.formatting_engine:apply:93 | 数值格式化失败: could not convert string to float: '计算机科学与技术学院'
2025-09-02 13:28:49.378 | WARNING  | src.modules.data_import.formatting_engine:apply:93 | 数值格式化失败: could not convert string to float: '教学单位专技人员'
2025-09-02 13:28:49.378 | WARNING  | src.modules.data_import.formatting_engine:apply:93 | 数值格式化失败: could not convert string to float: '张亚平'
2025-09-02 13:28:49.378 | WARNING  | src.modules.data_import.formatting_engine:apply:93 | 数值格式化失败: could not convert string to float: '计算机科学与技术学院'
2025-09-02 13:28:49.378 | WARNING  | src.modules.data_import.formatting_engine:apply:93 | 数值格式化失败: could not convert string to float: '教学院其它人员'
2025-09-02 13:28:49.378 | WARNING  | src.modules.data_import.formatting_engine:apply:93 | 数值格式化失败: could not convert string to float: '詹浩'
2025-09-02 13:28:49.378 | WARNING  | src.modules.data_import.formatting_engine:apply:93 | 数值格式化失败: could not convert string to float: '基建管理处'
2025-09-02 13:28:49.378 | WARNING  | src.modules.data_import.formatting_engine:apply:93 | 数值格式化失败: could not convert string to float: '管理单位人员'
2025-09-02 13:28:49.387 | WARNING  | src.modules.data_import.formatting_engine:apply:93 | 数值格式化失败: could not convert string to float: '王本志'
2025-09-02 13:28:49.389 | WARNING  | src.modules.data_import.formatting_engine:apply:93 | 数值格式化失败: could not convert string to float: '基建管理处'
2025-09-02 13:28:49.399 | WARNING  | src.modules.data_import.formatting_engine:apply:93 | 数值格式化失败: could not convert string to float: '管理单位人员'
2025-09-02 13:28:49.400 | WARNING  | src.modules.data_import.formatting_engine:apply:93 | 数值格式化失败: could not convert string to float: '金其武'
2025-09-02 13:28:49.401 | WARNING  | src.modules.data_import.formatting_engine:apply:93 | 数值格式化失败: could not convert string to float: '基础医学院'
2025-09-02 13:28:49.401 | WARNING  | src.modules.data_import.formatting_engine:apply:93 | 数值格式化失败: could not convert string to float: '教学院其它人员'
2025-09-02 13:28:49.401 | WARNING  | src.modules.data_import.formatting_engine:apply:93 | 数值格式化失败: could not convert string to float: '潘清容'
2025-09-02 13:28:49.403 | WARNING  | src.modules.data_import.formatting_engine:apply:93 | 数值格式化失败: could not convert string to float: '基础医学院'
2025-09-02 13:28:49.403 | WARNING  | src.modules.data_import.formatting_engine:apply:93 | 数值格式化失败: could not convert string to float: '教学单位专技人员'
2025-09-02 13:28:49.405 | WARNING  | src.modules.data_import.formatting_engine:apply:93 | 数值格式化失败: could not convert string to float: '童玉云'
2025-09-02 13:28:49.405 | WARNING  | src.modules.data_import.formatting_engine:apply:93 | 数值格式化失败: could not convert string to float: '后勤管理处'
2025-09-02 13:28:49.405 | WARNING  | src.modules.data_import.formatting_engine:apply:93 | 数值格式化失败: could not convert string to float: '管理单位人员'
2025-09-02 13:28:49.405 | WARNING  | src.modules.data_import.formatting_engine:apply:93 | 数值格式化失败: could not convert string to float: '郭胜康'
2025-09-02 13:28:49.405 | WARNING  | src.modules.data_import.formatting_engine:apply:93 | 数值格式化失败: could not convert string to float: '后勤管理处'
2025-09-02 13:28:49.409 | WARNING  | src.modules.data_import.formatting_engine:apply:93 | 数值格式化失败: could not convert string to float: '管理单位人员'
2025-09-02 13:28:49.410 | WARNING  | src.modules.data_import.formatting_engine:apply:93 | 数值格式化失败: could not convert string to float: '黄振兴'
2025-09-02 13:28:49.410 | WARNING  | src.modules.data_import.formatting_engine:apply:93 | 数值格式化失败: could not convert string to float: '后勤管理处'
2025-09-02 13:28:49.411 | WARNING  | src.modules.data_import.formatting_engine:apply:93 | 数值格式化失败: could not convert string to float: '管理单位人员'
2025-09-02 13:28:49.412 | WARNING  | src.modules.data_import.formatting_engine:apply:93 | 数值格式化失败: could not convert string to float: '程时华'
2025-09-02 13:28:49.412 | WARNING  | src.modules.data_import.formatting_engine:apply:93 | 数值格式化失败: could not convert string to float: '后勤管理处'
2025-09-02 13:28:49.414 | WARNING  | src.modules.data_import.formatting_engine:apply:93 | 数值格式化失败: could not convert string to float: '管理单位人员'
2025-09-02 13:28:49.414 | WARNING  | src.modules.data_import.formatting_engine:apply:93 | 数值格式化失败: could not convert string to float: '邓德华'
2025-09-02 13:28:49.414 | WARNING  | src.modules.data_import.formatting_engine:apply:93 | 数值格式化失败: could not convert string to float: '后勤管理处'
2025-09-02 13:28:49.416 | WARNING  | src.modules.data_import.formatting_engine:apply:93 | 数值格式化失败: could not convert string to float: '管理单位人员'
2025-09-02 13:28:49.416 | WARNING  | src.modules.data_import.formatting_engine:apply:93 | 数值格式化失败: could not convert string to float: '专永安'
2025-09-02 13:28:49.416 | WARNING  | src.modules.data_import.formatting_engine:apply:93 | 数值格式化失败: could not convert string to float: '后勤管理处'
2025-09-02 13:28:49.419 | WARNING  | src.modules.data_import.formatting_engine:apply:93 | 数值格式化失败: could not convert string to float: '管理单位人员'
2025-09-02 13:28:49.438 | WARNING  | src.modules.data_import.formatting_engine:apply:93 | 数值格式化失败: could not convert string to float: '刘燕庆'
2025-09-02 13:28:49.438 | WARNING  | src.modules.data_import.formatting_engine:apply:93 | 数值格式化失败: could not convert string to float: '核技术与化学生物学院'
2025-09-02 13:28:49.438 | WARNING  | src.modules.data_import.formatting_engine:apply:93 | 数值格式化失败: could not convert string to float: '管理单位人员'
2025-09-02 13:28:49.438 | WARNING  | src.modules.data_import.formatting_engine:apply:93 | 数值格式化失败: could not convert string to float: '甘紫昭'
2025-09-02 13:28:49.438 | WARNING  | src.modules.data_import.formatting_engine:apply:93 | 数值格式化失败: could not convert string to float: '核技术与化学生物学院'
2025-09-02 13:28:49.446 | WARNING  | src.modules.data_import.formatting_engine:apply:93 | 数值格式化失败: could not convert string to float: '教学单位专技人员'
2025-09-02 13:28:49.446 | WARNING  | src.modules.data_import.formatting_engine:apply:93 | 数值格式化失败: could not convert string to float: '王剑'
2025-09-02 13:28:49.448 | WARNING  | src.modules.data_import.formatting_engine:apply:93 | 数值格式化失败: could not convert string to float: '国有资产与实验室管理处'
2025-09-02 13:28:49.449 | WARNING  | src.modules.data_import.formatting_engine:apply:93 | 数值格式化失败: could not convert string to float: '管理单位人员'
2025-09-02 13:28:49.449 | WARNING  | src.modules.data_import.formatting_engine:apply:93 | 数值格式化失败: could not convert string to float: '段云秀'
2025-09-02 13:28:49.463 | WARNING  | src.modules.data_import.formatting_engine:apply:93 | 数值格式化失败: could not convert string to float: '电子与信息工程学院'
2025-09-02 13:28:49.463 | WARNING  | src.modules.data_import.formatting_engine:apply:93 | 数值格式化失败: could not convert string to float: '教学院其它人员'
2025-09-02 13:28:49.464 | WARNING  | src.modules.data_import.formatting_engine:apply:93 | 数值格式化失败: could not convert string to float: '周军'
2025-09-02 13:28:49.464 | WARNING  | src.modules.data_import.formatting_engine:apply:93 | 数值格式化失败: could not convert string to float: '党委办公室、主体办公室、机关党委'
2025-09-02 13:28:49.464 | WARNING  | src.modules.data_import.formatting_engine:apply:93 | 数值格式化失败: could not convert string to float: '管理单位人员'
2025-09-02 13:28:49.466 | WARNING  | src.modules.data_import.formatting_engine:apply:93 | 数值格式化失败: could not convert string to float: '王胜华'
2025-09-02 13:28:49.466 | WARNING  | src.modules.data_import.formatting_engine:apply:93 | 数值格式化失败: could not convert string to float: '党委办公室、主体办公室、机关党委'
2025-09-02 13:28:49.469 | WARNING  | src.modules.data_import.formatting_engine:apply:93 | 数值格式化失败: could not convert string to float: '管理单位人员'
2025-09-02 13:28:49.469 | WARNING  | src.modules.data_import.formatting_engine:apply:93 | 数值格式化失败: could not convert string to float: '陈红义'
2025-09-02 13:28:49.473 | WARNING  | src.modules.data_import.formatting_engine:apply:93 | 数值格式化失败: could not convert string to float: '档案馆'
2025-09-02 13:28:49.474 | WARNING  | src.modules.data_import.formatting_engine:apply:93 | 数值格式化失败: could not convert string to float: '管理单位人员'
2025-09-02 13:28:49.474 | WARNING  | src.modules.data_import.formatting_engine:apply:93 | 数值格式化失败: could not convert string to float: '陆琳'
2025-09-02 13:28:49.476 | WARNING  | src.modules.data_import.formatting_engine:apply:93 | 数值格式化失败: could not convert string to float: '档案馆'
2025-09-02 13:28:49.477 | WARNING  | src.modules.data_import.formatting_engine:apply:93 | 数值格式化失败: could not convert string to float: '教学院其它人员'
2025-09-02 13:28:49.477 | WARNING  | src.modules.data_import.formatting_engine:apply:93 | 数值格式化失败: could not convert string to float: '孙雪'
2025-09-02 13:28:49.477 | WARNING  | src.modules.data_import.formatting_engine:apply:93 | 数值格式化失败: could not convert string to float: '档案馆'
2025-09-02 13:28:49.477 | WARNING  | src.modules.data_import.formatting_engine:apply:93 | 数值格式化失败: could not convert string to float: '管理单位人员'
2025-09-02 13:28:49.477 | WARNING  | src.modules.data_import.formatting_engine:apply:93 | 数值格式化失败: could not convert string to float: '杨力睿'
2025-09-02 13:28:49.477 | WARNING  | src.modules.data_import.formatting_engine:apply:93 | 数值格式化失败: could not convert string to float: '创新创业学院'
2025-09-02 13:28:49.477 | WARNING  | src.modules.data_import.formatting_engine:apply:93 | 数值格式化失败: could not convert string to float: '管理单位人员'
2025-09-02 13:28:49.484 | WARNING  | src.modules.data_import.formatting_engine:apply:93 | 数值格式化失败: could not convert string to float: '邹婷'
2025-09-02 13:28:49.499 | WARNING  | src.modules.data_import.formatting_engine:apply:93 | 数值格式化失败: could not convert string to float: '财务处'
2025-09-02 13:28:49.501 | WARNING  | src.modules.data_import.formatting_engine:apply:93 | 数值格式化失败: could not convert string to float: '管理单位人员'
2025-09-02 13:28:49.501 | WARNING  | src.modules.data_import.formatting_engine:apply:93 | 数值格式化失败: could not convert string to float: '刘金'
2025-09-02 13:28:49.503 | WARNING  | src.modules.data_import.formatting_engine:apply:93 | 数值格式化失败: could not convert string to float: '保卫部（处）、综治与应急管理办公室'
2025-09-02 13:28:49.503 | WARNING  | src.modules.data_import.formatting_engine:apply:93 | 数值格式化失败: could not convert string to float: '管理单位人员'
2025-09-02 13:28:49.503 | WARNING  | src.modules.data_import.formatting_engine:apply:93 | 数值格式化失败: could not convert string to float: '卢映月'
2025-09-02 13:28:49.505 | WARNING  | src.modules.data_import.formatting_engine:apply:93 | 数值格式化失败: could not convert string to float: '药学院'
2025-09-02 13:28:49.505 | WARNING  | src.modules.data_import.formatting_engine:apply:93 | 数值格式化失败: could not convert string to float: '教学院其它人员'
2025-09-02 13:28:49.505 | WARNING  | src.modules.data_import.formatting_engine:apply:93 | 数值格式化失败: could not convert string to float: '宋俊义'
2025-09-02 13:28:49.505 | WARNING  | src.modules.data_import.formatting_engine:apply:93 | 数值格式化失败: could not convert string to float: '党委组织人事部、党委教师工作部、党校'
2025-09-02 13:28:49.505 | WARNING  | src.modules.data_import.formatting_engine:apply:93 | 数值格式化失败: could not convert string to float: '教学单位专技人员'
2025-09-02 13:28:49.509 | WARNING  | src.modules.data_import.formatting_engine:apply:93 | 数值格式化失败: could not convert string to float: '钱华'
2025-09-02 13:28:49.510 | WARNING  | src.modules.data_import.formatting_engine:apply:93 | 数值格式化失败: could not convert string to float: '艺术与设计学院'
2025-09-02 13:28:49.510 | WARNING  | src.modules.data_import.formatting_engine:apply:93 | 数值格式化失败: could not convert string to float: '教学单位管理单位人员'
2025-09-02 13:28:49.511 | WARNING  | src.modules.data_import.formatting_engine:apply:93 | 数值格式化失败: could not convert string to float: '吴雅妮'
2025-09-02 13:28:49.512 | WARNING  | src.modules.data_import.formatting_engine:apply:93 | 数值格式化失败: could not convert string to float: '口腔与眼视光医学院'
2025-09-02 13:28:49.512 | WARNING  | src.modules.data_import.formatting_engine:apply:93 | 数值格式化失败: could not convert string to float: '教学单位管理单位人员'
2025-09-02 13:28:49.514 | WARNING  | src.modules.data_import.formatting_engine:apply:93 | 数值格式化失败: could not convert string to float: '赵银利'
2025-09-02 13:28:49.514 | WARNING  | src.modules.data_import.formatting_engine:apply:93 | 数值格式化失败: could not convert string to float: '口腔与眼视光医学院'
2025-09-02 13:28:49.514 | WARNING  | src.modules.data_import.formatting_engine:apply:93 | 数值格式化失败: could not convert string to float: '教学单位管理单位人员'
2025-09-02 13:28:49.516 | WARNING  | src.modules.data_import.formatting_engine:apply:93 | 数值格式化失败: could not convert string to float: '刘朝'
2025-09-02 13:28:49.533 | WARNING  | src.modules.data_import.formatting_engine:apply:93 | 数值格式化失败: could not convert string to float: '口腔与眼视光医学院'
2025-09-02 13:28:49.537 | WARNING  | src.modules.data_import.formatting_engine:apply:93 | 数值格式化失败: could not convert string to float: '教学单位管理单位人员'
2025-09-02 13:28:49.537 | WARNING  | src.modules.data_import.formatting_engine:apply:93 | 数值格式化失败: could not convert string to float: '田宏霞'
2025-09-02 13:28:49.537 | WARNING  | src.modules.data_import.formatting_engine:apply:93 | 数值格式化失败: could not convert string to float: '后勤管理处'
2025-09-02 13:28:49.537 | WARNING  | src.modules.data_import.formatting_engine:apply:93 | 数值格式化失败: could not convert string to float: '管理单位人员'
2025-09-02 13:28:49.537 | WARNING  | src.modules.data_import.formatting_engine:apply:93 | 数值格式化失败: could not convert string to float: '喻言'
2025-09-02 13:28:49.537 | WARNING  | src.modules.data_import.formatting_engine:apply:93 | 数值格式化失败: could not convert string to float: '后勤管理处'
2025-09-02 13:28:49.537 | WARNING  | src.modules.data_import.formatting_engine:apply:93 | 数值格式化失败: could not convert string to float: '管理单位人员'
2025-09-02 13:28:49.537 | WARNING  | src.modules.data_import.formatting_engine:apply:93 | 数值格式化失败: could not convert string to float: '李军'
2025-09-02 13:28:49.545 | WARNING  | src.modules.data_import.formatting_engine:apply:93 | 数值格式化失败: could not convert string to float: '发展规划处'
2025-09-02 13:28:49.545 | WARNING  | src.modules.data_import.formatting_engine:apply:93 | 数值格式化失败: could not convert string to float: '管理单位人员'
2025-09-02 13:28:49.545 | WARNING  | src.modules.data_import.formatting_engine:apply:93 | 数值格式化失败: could not convert string to float: '何岳斌'
2025-09-02 13:28:49.545 | WARNING  | src.modules.data_import.formatting_engine:apply:93 | 数值格式化失败: could not convert string to float: '保卫部（处）'
2025-09-02 13:28:49.548 | WARNING  | src.modules.data_import.formatting_engine:apply:93 | 数值格式化失败: could not convert string to float: '管理单位人员'
2025-09-02 13:28:49.549 | WARNING  | src.modules.data_import.formatting_engine:apply:93 | 数值格式化失败: could not convert string to float: '鲁运鼎'
2025-09-02 13:28:49.562 | WARNING  | src.modules.data_import.formatting_engine:apply:93 | 数值格式化失败: could not convert string to float: '保卫处'
2025-09-02 13:28:49.562 | WARNING  | src.modules.data_import.formatting_engine:apply:93 | 数值格式化失败: could not convert string to float: '管理单位人员'
2025-09-02 13:28:49.565 | WARNING  | src.modules.data_import.formatting_engine:apply:93 | 数值格式化失败: could not convert string to float: '毛慜佶'
2025-09-02 13:28:49.565 | WARNING  | src.modules.data_import.formatting_engine:apply:93 | 数值格式化失败: could not convert string to float: '财务处'
2025-09-02 13:28:49.566 | WARNING  | src.modules.data_import.formatting_engine:apply:93 | 数值格式化失败: could not convert string to float: '管理单位人员'
2025-09-02 13:28:49.566 | WARNING  | src.modules.data_import.formatting_engine:apply:93 | 数值格式化失败: could not convert string to float: '孙语蔚'
2025-09-02 13:28:49.566 | WARNING  | src.modules.data_import.formatting_engine:apply:93 | 数值格式化失败: could not convert string to float: '音乐学院'
2025-09-02 13:28:49.568 | WARNING  | src.modules.data_import.formatting_engine:apply:93 | 数值格式化失败: could not convert string to float: '教学单位管理人员'
2025-09-02 13:28:49.569 | WARNING  | src.modules.data_import.formatting_engine:apply:93 | 数值格式化失败: could not convert string to float: '廖纬'
2025-09-02 13:28:49.569 | WARNING  | src.modules.data_import.formatting_engine:apply:93 | 数值格式化失败: could not convert string to float: '党委宣传部'
2025-09-02 13:28:49.569 | WARNING  | src.modules.data_import.formatting_engine:apply:93 | 数值格式化失败: could not convert string to float: '管理单位人员'
2025-09-02 13:28:49 - src.gui.unified_data_import_window - INFO - 预览数据加载完成（已应用格式化）: A岗职工 - 62 行
2025-09-02 13:28:49 - src.gui.unified_data_import_window - INFO - 切换到预览选项卡，已刷新格式化
2025-09-02 13:28:55 - src.gui.unified_data_import_window - INFO - 选项卡切换到: 🧹 数据处理 (索引: 1)
2025-09-02 13:28:55.975 | INFO     | src.modules.data_import.config_sync_manager:save_mapping:447 | 字段映射保存成功: mapping_config_20250902_132855
2025-09-02 13:28:55 - src.gui.unified_data_import_window - INFO - 字段映射实时保存成功: mapping_config_20250902_132855
2025-09-02 13:28:57 - src.gui.unified_data_import_window - INFO - 选项卡切换到: 🔗 字段映射 (索引: 0)
2025-09-02 13:28:57.100 | INFO     | src.modules.data_import.config_sync_manager:save_mapping:447 | 字段映射保存成功: mapping_config_20250902_132857
2025-09-02 13:28:57 - src.gui.unified_data_import_window - INFO - 字段映射实时保存成功: mapping_config_20250902_132857
2025-09-02 13:29:01 - src.gui.unified_data_import_window - INFO - 开始预览Sheet: A岗职工
2025-09-02 13:29:01.445 | INFO     | src.modules.data_import.excel_importer:validate_file:115 | 文件验证成功: 2025年5月份正式工工资（报财务)  最终版.xls (1.17MB)
2025-09-02 13:29:01.449 | INFO     | src.modules.data_import.excel_importer:import_data:274 | 开始导入Excel文件: 2025年5月份正式工工资（报财务)  最终版.xls
2025-09-02 13:29:01.449 | INFO     | src.utils.log_config:log_file_operation:336 | 文件C:\test\salary_changes\salary_changes\data\工资表\2025年5月份正式工工资（报财务)  最终版.xls: read
2025-09-02 13:29:01.555 | INFO     | src.modules.data_import.excel_importer:_import_normal_file:337 | [修复标识] Excel读取完成: 21列 (列过滤: 否)
2025-09-02 13:29:01.559 | INFO     | src.modules.data_import.excel_importer:_clean_data:602 | [修复标识] Excel导入器 _clean_data 方法 - 保留所有列结构修复已执行
2025-09-02 13:29:01.561 | INFO     | src.modules.data_import.excel_importer:_clean_data:613 | 数据清理完成: 保留所有 21 列 (原始 21 列)
2025-09-02 13:29:01.562 | INFO     | src.modules.data_import.excel_importer:_import_normal_file:362 | 导入完成: 10行 x 21列
2025-09-02 13:29:01.564 | INFO     | src.modules.data_import.excel_importer:import_data:301 | [修复标识] 数据导入最终完成: 10行 × 21列
2025-09-02 13:29:01 - src.gui.unified_data_import_window - INFO - 成功读取预览数据: 10 行, 21 列
2025-09-02 13:29:01 - src.gui.unified_data_import_window - INFO - 选项卡切换到: 👁️ 预览验证 (索引: 2)
2025-09-02 13:29:01.587 | INFO     | src.modules.data_import.config_sync_manager:save_mapping:447 | 字段映射保存成功: mapping_config_20250902_132901
2025-09-02 13:29:01 - src.gui.unified_data_import_window - INFO - 字段映射实时保存成功: mapping_config_20250902_132901
2025-09-02 13:29:01.587 | WARNING  | src.modules.data_import.formatting_engine:apply:93 | 数值格式化失败: could not convert string to float: '柳红胜'
2025-09-02 13:29:01.587 | WARNING  | src.modules.data_import.formatting_engine:apply:93 | 数值格式化失败: could not convert string to float: '自动化学院'
2025-09-02 13:29:01.594 | WARNING  | src.modules.data_import.formatting_engine:apply:93 | 数值格式化失败: could not convert string to float: '科研单位人员'
2025-09-02 13:29:01.598 | WARNING  | src.modules.data_import.formatting_engine:apply:93 | 数值格式化失败: could not convert string to float: '孙玲'
2025-09-02 13:29:01.598 | WARNING  | src.modules.data_import.formatting_engine:apply:93 | 数值格式化失败: could not convert string to float: '自动化学院'
2025-09-02 13:29:01.598 | WARNING  | src.modules.data_import.formatting_engine:apply:93 | 数值格式化失败: could not convert string to float: '教学院其它人员'
2025-09-02 13:29:01.601 | WARNING  | src.modules.data_import.formatting_engine:apply:93 | 数值格式化失败: could not convert string to float: '王祺景'
2025-09-02 13:29:01.601 | WARNING  | src.modules.data_import.formatting_engine:apply:93 | 数值格式化失败: could not convert string to float: '音乐学院'
2025-09-02 13:29:01.601 | WARNING  | src.modules.data_import.formatting_engine:apply:93 | 数值格式化失败: could not convert string to float: '教学院其它人员'
2025-09-02 13:29:01.603 | WARNING  | src.modules.data_import.formatting_engine:apply:93 | 数值格式化失败: could not convert string to float: '方艳'
2025-09-02 13:29:01.603 | WARNING  | src.modules.data_import.formatting_engine:apply:93 | 数值格式化失败: could not convert string to float: '艺术与设计学院'
2025-09-02 13:29:01.603 | WARNING  | src.modules.data_import.formatting_engine:apply:93 | 数值格式化失败: could not convert string to float: '教学单位专技人员'
2025-09-02 13:29:01.605 | WARNING  | src.modules.data_import.formatting_engine:apply:93 | 数值格式化失败: could not convert string to float: '刘蕤'
2025-09-02 13:29:01.605 | WARNING  | src.modules.data_import.formatting_engine:apply:93 | 数值格式化失败: could not convert string to float: '艺术与设计学院'
2025-09-02 13:29:01.605 | WARNING  | src.modules.data_import.formatting_engine:apply:93 | 数值格式化失败: could not convert string to float: '教学单位专技人员'
2025-09-02 13:29:01.605 | WARNING  | src.modules.data_import.formatting_engine:apply:93 | 数值格式化失败: could not convert string to float: '盛金锋'
2025-09-02 13:29:01.605 | WARNING  | src.modules.data_import.formatting_engine:apply:93 | 数值格式化失败: could not convert string to float: '信息中心'
2025-09-02 13:29:01.609 | WARNING  | src.modules.data_import.formatting_engine:apply:93 | 数值格式化失败: could not convert string to float: '管理单位人员'
2025-09-02 13:29:01.610 | WARNING  | src.modules.data_import.formatting_engine:apply:93 | 数值格式化失败: could not convert string to float: '陈姣梅'
2025-09-02 13:29:01.619 | WARNING  | src.modules.data_import.formatting_engine:apply:93 | 数值格式化失败: could not convert string to float: '校医院'
2025-09-02 13:29:01.619 | WARNING  | src.modules.data_import.formatting_engine:apply:93 | 数值格式化失败: could not convert string to float: '管理单位人员'
2025-09-02 13:29:01.623 | WARNING  | src.modules.data_import.formatting_engine:apply:93 | 数值格式化失败: could not convert string to float: '刘孟君'
2025-09-02 13:29:01.623 | WARNING  | src.modules.data_import.formatting_engine:apply:93 | 数值格式化失败: could not convert string to float: '校工会、妇联'
2025-09-02 13:29:01.623 | WARNING  | src.modules.data_import.formatting_engine:apply:93 | 数值格式化失败: could not convert string to float: '管理单位人员'
2025-09-02 13:29:01.625 | WARNING  | src.modules.data_import.formatting_engine:apply:93 | 数值格式化失败: could not convert string to float: '杨美玲'
2025-09-02 13:29:01.625 | WARNING  | src.modules.data_import.formatting_engine:apply:93 | 数值格式化失败: could not convert string to float: '图书馆'
2025-09-02 13:29:01.627 | WARNING  | src.modules.data_import.formatting_engine:apply:93 | 数值格式化失败: could not convert string to float: '管理单位人员'
2025-09-02 13:29:01.627 | WARNING  | src.modules.data_import.formatting_engine:apply:93 | 数值格式化失败: could not convert string to float: '陈仲华'
2025-09-02 13:29:01.627 | WARNING  | src.modules.data_import.formatting_engine:apply:93 | 数值格式化失败: could not convert string to float: '图书馆'
2025-09-02 13:29:01.627 | WARNING  | src.modules.data_import.formatting_engine:apply:93 | 数值格式化失败: could not convert string to float: '管理单位人员'
2025-09-02 13:29:01.627 | WARNING  | src.modules.data_import.formatting_engine:apply:93 | 数值格式化失败: could not convert string to float: '夏端辉'
2025-09-02 13:29:01.627 | WARNING  | src.modules.data_import.formatting_engine:apply:93 | 数值格式化失败: could not convert string to float: '图书馆'
2025-09-02 13:29:01.627 | WARNING  | src.modules.data_import.formatting_engine:apply:93 | 数值格式化失败: could not convert string to float: '管理单位人员'
2025-09-02 13:29:01.627 | WARNING  | src.modules.data_import.formatting_engine:apply:93 | 数值格式化失败: could not convert string to float: '徐颖霞'
2025-09-02 13:29:01.627 | WARNING  | src.modules.data_import.formatting_engine:apply:93 | 数值格式化失败: could not convert string to float: '图书馆'
2025-09-02 13:29:01.627 | WARNING  | src.modules.data_import.formatting_engine:apply:93 | 数值格式化失败: could not convert string to float: '管理单位人员'
2025-09-02 13:29:01.627 | WARNING  | src.modules.data_import.formatting_engine:apply:93 | 数值格式化失败: could not convert string to float: '孙刘萍'
2025-09-02 13:29:01.627 | WARNING  | src.modules.data_import.formatting_engine:apply:93 | 数值格式化失败: could not convert string to float: '数学与统计学院'
2025-09-02 13:29:01.637 | WARNING  | src.modules.data_import.formatting_engine:apply:93 | 数值格式化失败: could not convert string to float: '教学院其它人员'
2025-09-02 13:29:01.638 | WARNING  | src.modules.data_import.formatting_engine:apply:93 | 数值格式化失败: could not convert string to float: '周俊'
2025-09-02 13:29:01.638 | WARNING  | src.modules.data_import.formatting_engine:apply:93 | 数值格式化失败: could not convert string to float: '社会合作与校友工作处'
2025-09-02 13:29:01.638 | WARNING  | src.modules.data_import.formatting_engine:apply:93 | 数值格式化失败: could not convert string to float: '管理单位人员'
2025-09-02 13:29:01.638 | WARNING  | src.modules.data_import.formatting_engine:apply:93 | 数值格式化失败: could not convert string to float: '赵晓辰'
2025-09-02 13:29:01.638 | WARNING  | src.modules.data_import.formatting_engine:apply:93 | 数值格式化失败: could not convert string to float: '临床医学院'
2025-09-02 13:29:01.638 | WARNING  | src.modules.data_import.formatting_engine:apply:93 | 数值格式化失败: could not convert string to float: '教学院其它人员'
2025-09-02 13:29:01.638 | WARNING  | src.modules.data_import.formatting_engine:apply:93 | 数值格式化失败: could not convert string to float: '梅秀英'
2025-09-02 13:29:01.638 | WARNING  | src.modules.data_import.formatting_engine:apply:93 | 数值格式化失败: could not convert string to float: '临床医学院'
2025-09-02 13:29:01.638 | WARNING  | src.modules.data_import.formatting_engine:apply:93 | 数值格式化失败: could not convert string to float: '教学院其它人员'
2025-09-02 13:29:01.645 | WARNING  | src.modules.data_import.formatting_engine:apply:93 | 数值格式化失败: could not convert string to float: '李淑华'
2025-09-02 13:29:01.663 | WARNING  | src.modules.data_import.formatting_engine:apply:93 | 数值格式化失败: could not convert string to float: '口腔与眼视光医学院'
2025-09-02 13:29:01.663 | WARNING  | src.modules.data_import.formatting_engine:apply:93 | 数值格式化失败: could not convert string to float: '教学院其它人员'
2025-09-02 13:29:01.664 | WARNING  | src.modules.data_import.formatting_engine:apply:93 | 数值格式化失败: could not convert string to float: '杜艳'
2025-09-02 13:29:01.664 | WARNING  | src.modules.data_import.formatting_engine:apply:93 | 数值格式化失败: could not convert string to float: '科学技术发展院、科协'
2025-09-02 13:29:01.667 | WARNING  | src.modules.data_import.formatting_engine:apply:93 | 数值格式化失败: could not convert string to float: '管理单位人员'
2025-09-02 13:29:01.667 | WARNING  | src.modules.data_import.formatting_engine:apply:93 | 数值格式化失败: could not convert string to float: '刘昕芫'
2025-09-02 13:29:01.668 | WARNING  | src.modules.data_import.formatting_engine:apply:93 | 数值格式化失败: could not convert string to float: '经济与管理学院'
2025-09-02 13:29:01.668 | WARNING  | src.modules.data_import.formatting_engine:apply:93 | 数值格式化失败: could not convert string to float: '教学院其它人员'
2025-09-02 13:29:01.670 | WARNING  | src.modules.data_import.formatting_engine:apply:93 | 数值格式化失败: could not convert string to float: '林海'
2025-09-02 13:29:01.670 | WARNING  | src.modules.data_import.formatting_engine:apply:93 | 数值格式化失败: could not convert string to float: '经济与管理学院'
2025-09-02 13:29:01.670 | WARNING  | src.modules.data_import.formatting_engine:apply:93 | 数值格式化失败: could not convert string to float: '教学单位专技人员'
2025-09-02 13:29:01.670 | WARNING  | src.modules.data_import.formatting_engine:apply:93 | 数值格式化失败: could not convert string to float: '石波'
2025-09-02 13:29:01.670 | WARNING  | src.modules.data_import.formatting_engine:apply:93 | 数值格式化失败: could not convert string to float: '继续教育学院、职业技能培训鉴定中心'
2025-09-02 13:29:01.673 | WARNING  | src.modules.data_import.formatting_engine:apply:93 | 数值格式化失败: could not convert string to float: '管理单位人员'
2025-09-02 13:29:01.675 | WARNING  | src.modules.data_import.formatting_engine:apply:93 | 数值格式化失败: could not convert string to float: '言帆'
2025-09-02 13:29:01.675 | WARNING  | src.modules.data_import.formatting_engine:apply:93 | 数值格式化失败: could not convert string to float: '继续教育学院、职业技能培训鉴定中心'
2025-09-02 13:29:01.675 | WARNING  | src.modules.data_import.formatting_engine:apply:93 | 数值格式化失败: could not convert string to float: '管理单位人员'
2025-09-02 13:29:01.677 | WARNING  | src.modules.data_import.formatting_engine:apply:93 | 数值格式化失败: could not convert string to float: '夏春梅'
2025-09-02 13:29:01.677 | WARNING  | src.modules.data_import.formatting_engine:apply:93 | 数值格式化失败: could not convert string to float: '计算机科学与技术学院'
2025-09-02 13:29:01.677 | WARNING  | src.modules.data_import.formatting_engine:apply:93 | 数值格式化失败: could not convert string to float: '教学院其它人员'
2025-09-02 13:29:01.687 | WARNING  | src.modules.data_import.formatting_engine:apply:93 | 数值格式化失败: could not convert string to float: '肖文文'
2025-09-02 13:29:01.687 | WARNING  | src.modules.data_import.formatting_engine:apply:93 | 数值格式化失败: could not convert string to float: '计算机科学与技术学院'
2025-09-02 13:29:01.687 | WARNING  | src.modules.data_import.formatting_engine:apply:93 | 数值格式化失败: could not convert string to float: '教学院其它人员'
2025-09-02 13:29:01.687 | WARNING  | src.modules.data_import.formatting_engine:apply:93 | 数值格式化失败: could not convert string to float: '黄志成'
2025-09-02 13:29:01.687 | WARNING  | src.modules.data_import.formatting_engine:apply:93 | 数值格式化失败: could not convert string to float: '计算机科学与技术学院'
2025-09-02 13:29:01.687 | WARNING  | src.modules.data_import.formatting_engine:apply:93 | 数值格式化失败: could not convert string to float: '教学单位专技人员'
2025-09-02 13:29:01.687 | WARNING  | src.modules.data_import.formatting_engine:apply:93 | 数值格式化失败: could not convert string to float: '张亚平'
2025-09-02 13:29:01.687 | WARNING  | src.modules.data_import.formatting_engine:apply:93 | 数值格式化失败: could not convert string to float: '计算机科学与技术学院'
2025-09-02 13:29:01.687 | WARNING  | src.modules.data_import.formatting_engine:apply:93 | 数值格式化失败: could not convert string to float: '教学院其它人员'
2025-09-02 13:29:01.687 | WARNING  | src.modules.data_import.formatting_engine:apply:93 | 数值格式化失败: could not convert string to float: '詹浩'
2025-09-02 13:29:01.694 | WARNING  | src.modules.data_import.formatting_engine:apply:93 | 数值格式化失败: could not convert string to float: '基建管理处'
2025-09-02 13:29:01.701 | WARNING  | src.modules.data_import.formatting_engine:apply:93 | 数值格式化失败: could not convert string to float: '管理单位人员'
2025-09-02 13:29:01.703 | WARNING  | src.modules.data_import.formatting_engine:apply:93 | 数值格式化失败: could not convert string to float: '王本志'
2025-09-02 13:29:01.703 | WARNING  | src.modules.data_import.formatting_engine:apply:93 | 数值格式化失败: could not convert string to float: '基建管理处'
2025-09-02 13:29:01.703 | WARNING  | src.modules.data_import.formatting_engine:apply:93 | 数值格式化失败: could not convert string to float: '管理单位人员'
2025-09-02 13:29:01.705 | WARNING  | src.modules.data_import.formatting_engine:apply:93 | 数值格式化失败: could not convert string to float: '金其武'
2025-09-02 13:29:01.705 | WARNING  | src.modules.data_import.formatting_engine:apply:93 | 数值格式化失败: could not convert string to float: '基础医学院'
2025-09-02 13:29:01.705 | WARNING  | src.modules.data_import.formatting_engine:apply:93 | 数值格式化失败: could not convert string to float: '教学院其它人员'
2025-09-02 13:29:01.705 | WARNING  | src.modules.data_import.formatting_engine:apply:93 | 数值格式化失败: could not convert string to float: '潘清容'
2025-09-02 13:29:01.705 | WARNING  | src.modules.data_import.formatting_engine:apply:93 | 数值格式化失败: could not convert string to float: '基础医学院'
2025-09-02 13:29:01.709 | WARNING  | src.modules.data_import.formatting_engine:apply:93 | 数值格式化失败: could not convert string to float: '教学单位专技人员'
2025-09-02 13:29:01.709 | WARNING  | src.modules.data_import.formatting_engine:apply:93 | 数值格式化失败: could not convert string to float: '童玉云'
2025-09-02 13:29:01.710 | WARNING  | src.modules.data_import.formatting_engine:apply:93 | 数值格式化失败: could not convert string to float: '后勤管理处'
2025-09-02 13:29:01.719 | WARNING  | src.modules.data_import.formatting_engine:apply:93 | 数值格式化失败: could not convert string to float: '管理单位人员'
2025-09-02 13:29:01.722 | WARNING  | src.modules.data_import.formatting_engine:apply:93 | 数值格式化失败: could not convert string to float: '郭胜康'
2025-09-02 13:29:01.723 | WARNING  | src.modules.data_import.formatting_engine:apply:93 | 数值格式化失败: could not convert string to float: '后勤管理处'
2025-09-02 13:29:01.724 | WARNING  | src.modules.data_import.formatting_engine:apply:93 | 数值格式化失败: could not convert string to float: '管理单位人员'
2025-09-02 13:29:01.724 | WARNING  | src.modules.data_import.formatting_engine:apply:93 | 数值格式化失败: could not convert string to float: '黄振兴'
2025-09-02 13:29:01.726 | WARNING  | src.modules.data_import.formatting_engine:apply:93 | 数值格式化失败: could not convert string to float: '后勤管理处'
2025-09-02 13:29:01.727 | WARNING  | src.modules.data_import.formatting_engine:apply:93 | 数值格式化失败: could not convert string to float: '管理单位人员'
2025-09-02 13:29:01.728 | WARNING  | src.modules.data_import.formatting_engine:apply:93 | 数值格式化失败: could not convert string to float: '程时华'
2025-09-02 13:29:01.728 | WARNING  | src.modules.data_import.formatting_engine:apply:93 | 数值格式化失败: could not convert string to float: '后勤管理处'
2025-09-02 13:29:01.730 | WARNING  | src.modules.data_import.formatting_engine:apply:93 | 数值格式化失败: could not convert string to float: '管理单位人员'
2025-09-02 13:29:01.730 | WARNING  | src.modules.data_import.formatting_engine:apply:93 | 数值格式化失败: could not convert string to float: '邓德华'
2025-09-02 13:29:01.730 | WARNING  | src.modules.data_import.formatting_engine:apply:93 | 数值格式化失败: could not convert string to float: '后勤管理处'
2025-09-02 13:29:01.730 | WARNING  | src.modules.data_import.formatting_engine:apply:93 | 数值格式化失败: could not convert string to float: '管理单位人员'
2025-09-02 13:29:01.730 | WARNING  | src.modules.data_import.formatting_engine:apply:93 | 数值格式化失败: could not convert string to float: '专永安'
2025-09-02 13:29:01.730 | WARNING  | src.modules.data_import.formatting_engine:apply:93 | 数值格式化失败: could not convert string to float: '后勤管理处'
2025-09-02 13:29:01.730 | WARNING  | src.modules.data_import.formatting_engine:apply:93 | 数值格式化失败: could not convert string to float: '管理单位人员'
2025-09-02 13:29:01.730 | WARNING  | src.modules.data_import.formatting_engine:apply:93 | 数值格式化失败: could not convert string to float: '刘燕庆'
2025-09-02 13:29:01.730 | WARNING  | src.modules.data_import.formatting_engine:apply:93 | 数值格式化失败: could not convert string to float: '核技术与化学生物学院'
2025-09-02 13:29:01.737 | WARNING  | src.modules.data_import.formatting_engine:apply:93 | 数值格式化失败: could not convert string to float: '管理单位人员'
2025-09-02 13:29:01.737 | WARNING  | src.modules.data_import.formatting_engine:apply:93 | 数值格式化失败: could not convert string to float: '甘紫昭'
2025-09-02 13:29:01.737 | WARNING  | src.modules.data_import.formatting_engine:apply:93 | 数值格式化失败: could not convert string to float: '核技术与化学生物学院'
2025-09-02 13:29:01.737 | WARNING  | src.modules.data_import.formatting_engine:apply:93 | 数值格式化失败: could not convert string to float: '教学单位专技人员'
2025-09-02 13:29:01.737 | WARNING  | src.modules.data_import.formatting_engine:apply:93 | 数值格式化失败: could not convert string to float: '王剑'
2025-09-02 13:29:01.737 | WARNING  | src.modules.data_import.formatting_engine:apply:93 | 数值格式化失败: could not convert string to float: '国有资产与实验室管理处'
2025-09-02 13:29:01.737 | WARNING  | src.modules.data_import.formatting_engine:apply:93 | 数值格式化失败: could not convert string to float: '管理单位人员'
2025-09-02 13:29:01.737 | WARNING  | src.modules.data_import.formatting_engine:apply:93 | 数值格式化失败: could not convert string to float: '段云秀'
2025-09-02 13:29:01.737 | WARNING  | src.modules.data_import.formatting_engine:apply:93 | 数值格式化失败: could not convert string to float: '电子与信息工程学院'
2025-09-02 13:29:01.737 | WARNING  | src.modules.data_import.formatting_engine:apply:93 | 数值格式化失败: could not convert string to float: '教学院其它人员'
2025-09-02 13:29:01.744 | WARNING  | src.modules.data_import.formatting_engine:apply:93 | 数值格式化失败: could not convert string to float: '周军'
2025-09-02 13:29:01.755 | WARNING  | src.modules.data_import.formatting_engine:apply:93 | 数值格式化失败: could not convert string to float: '党委办公室、主体办公室、机关党委'
2025-09-02 13:29:01.755 | WARNING  | src.modules.data_import.formatting_engine:apply:93 | 数值格式化失败: could not convert string to float: '管理单位人员'
2025-09-02 13:29:01.759 | WARNING  | src.modules.data_import.formatting_engine:apply:93 | 数值格式化失败: could not convert string to float: '王胜华'
2025-09-02 13:29:01.760 | WARNING  | src.modules.data_import.formatting_engine:apply:93 | 数值格式化失败: could not convert string to float: '党委办公室、主体办公室、机关党委'
2025-09-02 13:29:01.760 | WARNING  | src.modules.data_import.formatting_engine:apply:93 | 数值格式化失败: could not convert string to float: '管理单位人员'
2025-09-02 13:29:01.761 | WARNING  | src.modules.data_import.formatting_engine:apply:93 | 数值格式化失败: could not convert string to float: '陈红义'
2025-09-02 13:29:01.762 | WARNING  | src.modules.data_import.formatting_engine:apply:93 | 数值格式化失败: could not convert string to float: '档案馆'
2025-09-02 13:29:01.762 | WARNING  | src.modules.data_import.formatting_engine:apply:93 | 数值格式化失败: could not convert string to float: '管理单位人员'
2025-09-02 13:29:01.762 | WARNING  | src.modules.data_import.formatting_engine:apply:93 | 数值格式化失败: could not convert string to float: '陆琳'
2025-09-02 13:29:01.764 | WARNING  | src.modules.data_import.formatting_engine:apply:93 | 数值格式化失败: could not convert string to float: '档案馆'
2025-09-02 13:29:01.764 | WARNING  | src.modules.data_import.formatting_engine:apply:93 | 数值格式化失败: could not convert string to float: '教学院其它人员'
2025-09-02 13:29:01.766 | WARNING  | src.modules.data_import.formatting_engine:apply:93 | 数值格式化失败: could not convert string to float: '孙雪'
2025-09-02 13:29:01.766 | WARNING  | src.modules.data_import.formatting_engine:apply:93 | 数值格式化失败: could not convert string to float: '档案馆'
2025-09-02 13:29:01.766 | WARNING  | src.modules.data_import.formatting_engine:apply:93 | 数值格式化失败: could not convert string to float: '管理单位人员'
2025-09-02 13:29:01.766 | WARNING  | src.modules.data_import.formatting_engine:apply:93 | 数值格式化失败: could not convert string to float: '杨力睿'
2025-09-02 13:29:01.769 | WARNING  | src.modules.data_import.formatting_engine:apply:93 | 数值格式化失败: could not convert string to float: '创新创业学院'
2025-09-02 13:29:01.769 | WARNING  | src.modules.data_import.formatting_engine:apply:93 | 数值格式化失败: could not convert string to float: '管理单位人员'
2025-09-02 13:29:01.769 | WARNING  | src.modules.data_import.formatting_engine:apply:93 | 数值格式化失败: could not convert string to float: '邹婷'
2025-09-02 13:29:01.769 | WARNING  | src.modules.data_import.formatting_engine:apply:93 | 数值格式化失败: could not convert string to float: '财务处'
2025-09-02 13:29:01.769 | WARNING  | src.modules.data_import.formatting_engine:apply:93 | 数值格式化失败: could not convert string to float: '管理单位人员'
2025-09-02 13:29:01.769 | WARNING  | src.modules.data_import.formatting_engine:apply:93 | 数值格式化失败: could not convert string to float: '刘金'
2025-09-02 13:29:01.773 | WARNING  | src.modules.data_import.formatting_engine:apply:93 | 数值格式化失败: could not convert string to float: '保卫部（处）、综治与应急管理办公室'
2025-09-02 13:29:01.773 | WARNING  | src.modules.data_import.formatting_engine:apply:93 | 数值格式化失败: could not convert string to float: '管理单位人员'
2025-09-02 13:29:01.775 | WARNING  | src.modules.data_import.formatting_engine:apply:93 | 数值格式化失败: could not convert string to float: '卢映月'
2025-09-02 13:29:01.775 | WARNING  | src.modules.data_import.formatting_engine:apply:93 | 数值格式化失败: could not convert string to float: '药学院'
2025-09-02 13:29:01.776 | WARNING  | src.modules.data_import.formatting_engine:apply:93 | 数值格式化失败: could not convert string to float: '教学院其它人员'
2025-09-02 13:29:01.795 | WARNING  | src.modules.data_import.formatting_engine:apply:93 | 数值格式化失败: could not convert string to float: '宋俊义'
2025-09-02 13:29:01.795 | WARNING  | src.modules.data_import.formatting_engine:apply:93 | 数值格式化失败: could not convert string to float: '党委组织人事部、党委教师工作部、党校'
2025-09-02 13:29:01.795 | WARNING  | src.modules.data_import.formatting_engine:apply:93 | 数值格式化失败: could not convert string to float: '教学单位专技人员'
2025-09-02 13:29:01.795 | WARNING  | src.modules.data_import.formatting_engine:apply:93 | 数值格式化失败: could not convert string to float: '钱华'
2025-09-02 13:29:01.798 | WARNING  | src.modules.data_import.formatting_engine:apply:93 | 数值格式化失败: could not convert string to float: '艺术与设计学院'
2025-09-02 13:29:01.798 | WARNING  | src.modules.data_import.formatting_engine:apply:93 | 数值格式化失败: could not convert string to float: '教学单位管理单位人员'
2025-09-02 13:29:01.801 | WARNING  | src.modules.data_import.formatting_engine:apply:93 | 数值格式化失败: could not convert string to float: '吴雅妮'
2025-09-02 13:29:01.801 | WARNING  | src.modules.data_import.formatting_engine:apply:93 | 数值格式化失败: could not convert string to float: '口腔与眼视光医学院'
2025-09-02 13:29:01.801 | WARNING  | src.modules.data_import.formatting_engine:apply:93 | 数值格式化失败: could not convert string to float: '教学单位管理单位人员'
2025-09-02 13:29:01.803 | WARNING  | src.modules.data_import.formatting_engine:apply:93 | 数值格式化失败: could not convert string to float: '赵银利'
2025-09-02 13:29:01.803 | WARNING  | src.modules.data_import.formatting_engine:apply:93 | 数值格式化失败: could not convert string to float: '口腔与眼视光医学院'
2025-09-02 13:29:01.805 | WARNING  | src.modules.data_import.formatting_engine:apply:93 | 数值格式化失败: could not convert string to float: '教学单位管理单位人员'
2025-09-02 13:29:01.805 | WARNING  | src.modules.data_import.formatting_engine:apply:93 | 数值格式化失败: could not convert string to float: '刘朝'
2025-09-02 13:29:01.805 | WARNING  | src.modules.data_import.formatting_engine:apply:93 | 数值格式化失败: could not convert string to float: '口腔与眼视光医学院'
2025-09-02 13:29:01.805 | WARNING  | src.modules.data_import.formatting_engine:apply:93 | 数值格式化失败: could not convert string to float: '教学单位管理单位人员'
2025-09-02 13:29:01.809 | WARNING  | src.modules.data_import.formatting_engine:apply:93 | 数值格式化失败: could not convert string to float: '田宏霞'
2025-09-02 13:29:01.809 | WARNING  | src.modules.data_import.formatting_engine:apply:93 | 数值格式化失败: could not convert string to float: '后勤管理处'
2025-09-02 13:29:01.819 | WARNING  | src.modules.data_import.formatting_engine:apply:93 | 数值格式化失败: could not convert string to float: '管理单位人员'
2025-09-02 13:29:01.819 | WARNING  | src.modules.data_import.formatting_engine:apply:93 | 数值格式化失败: could not convert string to float: '喻言'
2025-09-02 13:29:01.823 | WARNING  | src.modules.data_import.formatting_engine:apply:93 | 数值格式化失败: could not convert string to float: '后勤管理处'
2025-09-02 13:29:01.825 | WARNING  | src.modules.data_import.formatting_engine:apply:93 | 数值格式化失败: could not convert string to float: '管理单位人员'
2025-09-02 13:29:01.825 | WARNING  | src.modules.data_import.formatting_engine:apply:93 | 数值格式化失败: could not convert string to float: '李军'
2025-09-02 13:29:01.825 | WARNING  | src.modules.data_import.formatting_engine:apply:93 | 数值格式化失败: could not convert string to float: '发展规划处'
2025-09-02 13:29:01.827 | WARNING  | src.modules.data_import.formatting_engine:apply:93 | 数值格式化失败: could not convert string to float: '管理单位人员'
2025-09-02 13:29:01.827 | WARNING  | src.modules.data_import.formatting_engine:apply:93 | 数值格式化失败: could not convert string to float: '何岳斌'
2025-09-02 13:29:01.827 | WARNING  | src.modules.data_import.formatting_engine:apply:93 | 数值格式化失败: could not convert string to float: '保卫部（处）'
2025-09-02 13:29:01.827 | WARNING  | src.modules.data_import.formatting_engine:apply:93 | 数值格式化失败: could not convert string to float: '管理单位人员'
2025-09-02 13:29:01.827 | WARNING  | src.modules.data_import.formatting_engine:apply:93 | 数值格式化失败: could not convert string to float: '鲁运鼎'
2025-09-02 13:29:01.827 | WARNING  | src.modules.data_import.formatting_engine:apply:93 | 数值格式化失败: could not convert string to float: '保卫处'
2025-09-02 13:29:01.827 | WARNING  | src.modules.data_import.formatting_engine:apply:93 | 数值格式化失败: could not convert string to float: '管理单位人员'
2025-09-02 13:29:01.827 | WARNING  | src.modules.data_import.formatting_engine:apply:93 | 数值格式化失败: could not convert string to float: '毛慜佶'
2025-09-02 13:29:01.827 | WARNING  | src.modules.data_import.formatting_engine:apply:93 | 数值格式化失败: could not convert string to float: '财务处'
2025-09-02 13:29:01.827 | WARNING  | src.modules.data_import.formatting_engine:apply:93 | 数值格式化失败: could not convert string to float: '管理单位人员'
2025-09-02 13:29:01.827 | WARNING  | src.modules.data_import.formatting_engine:apply:93 | 数值格式化失败: could not convert string to float: '孙语蔚'
2025-09-02 13:29:01.827 | WARNING  | src.modules.data_import.formatting_engine:apply:93 | 数值格式化失败: could not convert string to float: '音乐学院'
2025-09-02 13:29:01.837 | WARNING  | src.modules.data_import.formatting_engine:apply:93 | 数值格式化失败: could not convert string to float: '教学单位管理人员'
2025-09-02 13:29:01.837 | WARNING  | src.modules.data_import.formatting_engine:apply:93 | 数值格式化失败: could not convert string to float: '廖纬'
2025-09-02 13:29:01.837 | WARNING  | src.modules.data_import.formatting_engine:apply:93 | 数值格式化失败: could not convert string to float: '党委宣传部'
2025-09-02 13:29:01.837 | WARNING  | src.modules.data_import.formatting_engine:apply:93 | 数值格式化失败: could not convert string to float: '管理单位人员'
2025-09-02 13:29:01 - src.gui.unified_data_import_window - INFO - 预览数据加载完成（已应用格式化）: A岗职工 - 62 行
2025-09-02 13:29:01 - src.gui.unified_data_import_window - INFO - 切换到预览选项卡，已刷新格式化
2025-09-02 13:29:01.855 | WARNING  | src.modules.data_import.formatting_engine:apply:93 | 数值格式化失败: could not convert string to float: '柳红胜'
2025-09-02 13:29:01.855 | WARNING  | src.modules.data_import.formatting_engine:apply:93 | 数值格式化失败: could not convert string to float: '自动化学院'
2025-09-02 13:29:01.855 | WARNING  | src.modules.data_import.formatting_engine:apply:93 | 数值格式化失败: could not convert string to float: '科研单位人员'
2025-09-02 13:29:01.859 | WARNING  | src.modules.data_import.formatting_engine:apply:93 | 数值格式化失败: could not convert string to float: '孙玲'
2025-09-02 13:29:01.859 | WARNING  | src.modules.data_import.formatting_engine:apply:93 | 数值格式化失败: could not convert string to float: '自动化学院'
2025-09-02 13:29:01.860 | WARNING  | src.modules.data_import.formatting_engine:apply:93 | 数值格式化失败: could not convert string to float: '教学院其它人员'
2025-09-02 13:29:01.861 | WARNING  | src.modules.data_import.formatting_engine:apply:93 | 数值格式化失败: could not convert string to float: '王祺景'
2025-09-02 13:29:01.862 | WARNING  | src.modules.data_import.formatting_engine:apply:93 | 数值格式化失败: could not convert string to float: '音乐学院'
2025-09-02 13:29:01.862 | WARNING  | src.modules.data_import.formatting_engine:apply:93 | 数值格式化失败: could not convert string to float: '教学院其它人员'
2025-09-02 13:29:01.864 | WARNING  | src.modules.data_import.formatting_engine:apply:93 | 数值格式化失败: could not convert string to float: '方艳'
2025-09-02 13:29:01.864 | WARNING  | src.modules.data_import.formatting_engine:apply:93 | 数值格式化失败: could not convert string to float: '艺术与设计学院'
2025-09-02 13:29:01.864 | WARNING  | src.modules.data_import.formatting_engine:apply:93 | 数值格式化失败: could not convert string to float: '教学单位专技人员'
2025-09-02 13:29:01.866 | WARNING  | src.modules.data_import.formatting_engine:apply:93 | 数值格式化失败: could not convert string to float: '刘蕤'
2025-09-02 13:29:01.866 | WARNING  | src.modules.data_import.formatting_engine:apply:93 | 数值格式化失败: could not convert string to float: '艺术与设计学院'
2025-09-02 13:29:01.866 | WARNING  | src.modules.data_import.formatting_engine:apply:93 | 数值格式化失败: could not convert string to float: '教学单位专技人员'
2025-09-02 13:29:01.869 | WARNING  | src.modules.data_import.formatting_engine:apply:93 | 数值格式化失败: could not convert string to float: '盛金锋'
2025-09-02 13:29:01.870 | WARNING  | src.modules.data_import.formatting_engine:apply:93 | 数值格式化失败: could not convert string to float: '信息中心'
2025-09-02 13:29:01.870 | WARNING  | src.modules.data_import.formatting_engine:apply:93 | 数值格式化失败: could not convert string to float: '管理单位人员'
2025-09-02 13:29:01.870 | WARNING  | src.modules.data_import.formatting_engine:apply:93 | 数值格式化失败: could not convert string to float: '陈姣梅'
2025-09-02 13:29:01.870 | WARNING  | src.modules.data_import.formatting_engine:apply:93 | 数值格式化失败: could not convert string to float: '校医院'
2025-09-02 13:29:01.874 | WARNING  | src.modules.data_import.formatting_engine:apply:93 | 数值格式化失败: could not convert string to float: '管理单位人员'
2025-09-02 13:29:01.875 | WARNING  | src.modules.data_import.formatting_engine:apply:93 | 数值格式化失败: could not convert string to float: '刘孟君'
2025-09-02 13:29:01.875 | WARNING  | src.modules.data_import.formatting_engine:apply:93 | 数值格式化失败: could not convert string to float: '校工会、妇联'
2025-09-02 13:29:01.876 | WARNING  | src.modules.data_import.formatting_engine:apply:93 | 数值格式化失败: could not convert string to float: '管理单位人员'
2025-09-02 13:29:01.887 | WARNING  | src.modules.data_import.formatting_engine:apply:93 | 数值格式化失败: could not convert string to float: '杨美玲'
2025-09-02 13:29:01.887 | WARNING  | src.modules.data_import.formatting_engine:apply:93 | 数值格式化失败: could not convert string to float: '图书馆'
2025-09-02 13:29:01.887 | WARNING  | src.modules.data_import.formatting_engine:apply:93 | 数值格式化失败: could not convert string to float: '管理单位人员'
2025-09-02 13:29:01.894 | WARNING  | src.modules.data_import.formatting_engine:apply:93 | 数值格式化失败: could not convert string to float: '陈仲华'
2025-09-02 13:29:01.894 | WARNING  | src.modules.data_import.formatting_engine:apply:93 | 数值格式化失败: could not convert string to float: '图书馆'
2025-09-02 13:29:01.894 | WARNING  | src.modules.data_import.formatting_engine:apply:93 | 数值格式化失败: could not convert string to float: '管理单位人员'
2025-09-02 13:29:01 - src.gui.unified_data_import_window - INFO - 预览数据加载完成（已应用格式化）: A岗职工 - 10 行
2025-09-02 13:29:06 - src.gui.unified_data_import_window - INFO - 选项卡切换到: 📝 字段类型 (索引: 3)
2025-09-02 13:29:06.698 | INFO     | src.modules.data_import.config_sync_manager:save_mapping:447 | 字段映射保存成功: mapping_config_20250902_132906
2025-09-02 13:29:06 - src.gui.unified_data_import_window - INFO - 字段映射实时保存成功: mapping_config_20250902_132906
2025-09-02 13:29:08 - src.gui.unified_data_import_window - INFO - 选项卡切换到: 🧹 数据处理 (索引: 1)
2025-09-02 13:29:08.027 | INFO     | src.modules.data_import.config_sync_manager:save_mapping:447 | 字段映射保存成功: mapping_config_20250902_132908
2025-09-02 13:29:08 - src.gui.unified_data_import_window - INFO - 字段映射实时保存成功: mapping_config_20250902_132908
2025-09-02 13:29:09 - src.gui.unified_data_import_window - INFO - 选项卡切换到: 🔗 字段映射 (索引: 0)
2025-09-02 13:29:09.078 | INFO     | src.modules.data_import.config_sync_manager:save_mapping:447 | 字段映射保存成功: mapping_config_20250902_132909
2025-09-02 13:29:09 - src.gui.unified_data_import_window - INFO - 字段映射实时保存成功: mapping_config_20250902_132909
2025-09-02 13:29:20.715 | INFO     | src.modules.data_import.config_sync_manager:save_mapping:447 | 字段映射保存成功: mapping_config_20250902_132920
2025-09-02 13:29:20 - src.gui.unified_data_import_window - INFO - 字段映射实时保存成功: mapping_config_20250902_132920
2025-09-02 13:29:22.589 | INFO     | src.modules.data_import.config_sync_manager:save_mapping:447 | 字段映射保存成功: mapping_config_20250902_132922
2025-09-02 13:29:22 - src.gui.unified_data_import_window - INFO - 字段映射实时保存成功: mapping_config_20250902_132922
2025-09-02 13:29:24.380 | INFO     | src.modules.data_import.config_sync_manager:save_mapping:447 | 字段映射保存成功: mapping_config_20250902_132924
2025-09-02 13:29:24 - src.gui.unified_data_import_window - INFO - 字段映射实时保存成功: mapping_config_20250902_132924
2025-09-02 13:29:26.876 | INFO     | src.modules.data_import.config_sync_manager:save_mapping:447 | 字段映射保存成功: mapping_config_20250902_132926
2025-09-02 13:29:26 - src.gui.unified_data_import_window - INFO - 字段映射实时保存成功: mapping_config_20250902_132926
2025-09-02 13:29:29 - src.gui.unified_data_import_window - INFO - 选项卡切换到: 👁️ 预览验证 (索引: 2)
2025-09-02 13:29:29.978 | INFO     | src.modules.data_import.config_sync_manager:save_mapping:447 | 字段映射保存成功: mapping_config_20250902_132929
2025-09-02 13:29:29 - src.gui.unified_data_import_window - INFO - 字段映射实时保存成功: mapping_config_20250902_132929
2025-09-02 13:29:29.978 | WARNING  | src.modules.data_import.formatting_engine:apply:93 | 数值格式化失败: could not convert string to float: '科研单位人员'
2025-09-02 13:29:29.978 | WARNING  | src.modules.data_import.formatting_engine:apply:93 | 数值格式化失败: could not convert string to float: '教学院其它人员'
2025-09-02 13:29:29.978 | WARNING  | src.modules.data_import.formatting_engine:apply:93 | 数值格式化失败: could not convert string to float: '教学院其它人员'
2025-09-02 13:29:29.978 | WARNING  | src.modules.data_import.formatting_engine:apply:93 | 数值格式化失败: could not convert string to float: '教学单位专技人员'
2025-09-02 13:29:29.978 | WARNING  | src.modules.data_import.formatting_engine:apply:93 | 数值格式化失败: could not convert string to float: '教学单位专技人员'
2025-09-02 13:29:29.978 | WARNING  | src.modules.data_import.formatting_engine:apply:93 | 数值格式化失败: could not convert string to float: '管理单位人员'
2025-09-02 13:29:29.978 | WARNING  | src.modules.data_import.formatting_engine:apply:93 | 数值格式化失败: could not convert string to float: '管理单位人员'
2025-09-02 13:29:29.987 | WARNING  | src.modules.data_import.formatting_engine:apply:93 | 数值格式化失败: could not convert string to float: '管理单位人员'
2025-09-02 13:29:29.987 | WARNING  | src.modules.data_import.formatting_engine:apply:93 | 数值格式化失败: could not convert string to float: '管理单位人员'
2025-09-02 13:29:29.987 | WARNING  | src.modules.data_import.formatting_engine:apply:93 | 数值格式化失败: could not convert string to float: '管理单位人员'
2025-09-02 13:29:29 - src.gui.unified_data_import_window - INFO - 预览数据加载完成（已应用格式化）: A岗职工 - 10 行
2025-09-02 13:29:29 - src.gui.unified_data_import_window - INFO - 切换到预览选项卡，已刷新格式化
2025-09-02 13:29:37 - src.gui.unified_data_import_window - INFO - 选项卡切换到: 🔗 字段映射 (索引: 0)
2025-09-02 13:29:37.429 | INFO     | src.modules.data_import.config_sync_manager:save_mapping:447 | 字段映射保存成功: mapping_config_20250902_132937
2025-09-02 13:29:37 - src.gui.unified_data_import_window - INFO - 字段映射实时保存成功: mapping_config_20250902_132937
2025-09-02 13:29:41.673 | INFO     | src.modules.data_import.config_sync_manager:save_mapping:447 | 字段映射保存成功: mapping_config_20250902_132941
2025-09-02 13:29:41 - src.gui.unified_data_import_window - INFO - 字段映射实时保存成功: mapping_config_20250902_132941
2025-09-02 13:29:44.251 | INFO     | src.modules.data_import.config_sync_manager:save_mapping:447 | 字段映射保存成功: mapping_config_20250902_132944
2025-09-02 13:29:44 - src.gui.unified_data_import_window - INFO - 字段映射实时保存成功: mapping_config_20250902_132944
2025-09-02 13:29:45 - src.gui.unified_data_import_window - INFO - 选项卡切换到: 👁️ 预览验证 (索引: 2)
2025-09-02 13:29:45.571 | INFO     | src.modules.data_import.config_sync_manager:save_mapping:447 | 字段映射保存成功: mapping_config_20250902_132945
2025-09-02 13:29:45 - src.gui.unified_data_import_window - INFO - 字段映射实时保存成功: mapping_config_20250902_132945
2025-09-02 13:29:45 - src.gui.unified_data_import_window - INFO - 预览数据加载完成（已应用格式化）: A岗职工 - 10 行
2025-09-02 13:29:45 - src.gui.unified_data_import_window - INFO - 切换到预览选项卡，已刷新格式化
2025-09-02 13:29:59 - src.gui.unified_data_import_window - INFO - 选中Sheet: 离休人员工资表
2025-09-02 13:29:59.213 | INFO     | src.modules.data_import.sheet_config_manager:get_or_create_config:161 | 为Sheet '离休人员工资表' 创建默认配置
2025-09-02 13:29:59 - src.gui.unified_data_import_window - INFO - 当前Sheet变化: 离休人员工资表
2025-09-02 13:29:59.216 | INFO     | src.modules.data_import.sheet_config_manager:get_or_create_config:161 | 为Sheet '离休人员工资表' 创建默认配置
2025-09-02 13:29:59.219 | INFO     | src.gui.widgets.data_processing_widget:_load_template_list:732 | 加载了 10 个模板
2025-09-02 13:29:59.219 | INFO     | src.gui.widgets.data_processing_widget:update_for_sheet:416 | 数据处理配置已切换到Sheet: 离休人员工资表
2025-09-02 13:29:59 - src.gui.unified_data_import_window - INFO - 更新字段映射配置: 离休人员工资表
2025-09-02 13:29:59.219 | INFO     | src.modules.data_import.excel_importer:validate_file:115 | 文件验证成功: 2025年5月份正式工工资（报财务)  最终版.xls (1.17MB)
2025-09-02 13:29:59.219 | INFO     | src.modules.data_import.excel_importer:import_data:274 | 开始导入Excel文件: 2025年5月份正式工工资（报财务)  最终版.xls
2025-09-02 13:29:59.219 | INFO     | src.utils.log_config:log_file_operation:336 | 文件C:\test\salary_changes\salary_changes\data\工资表\2025年5月份正式工工资（报财务)  最终版.xls: read
2025-09-02 13:29:59.325 | INFO     | src.modules.data_import.excel_importer:_import_normal_file:337 | [修复标识] Excel读取完成: 16列 (列过滤: 否)
2025-09-02 13:29:59.327 | INFO     | src.modules.data_import.excel_importer:_clean_data:602 | [修复标识] Excel导入器 _clean_data 方法 - 保留所有列结构修复已执行
2025-09-02 13:29:59.327 | INFO     | src.modules.data_import.excel_importer:_clean_data:613 | 数据清理完成: 保留所有 16 列 (原始 16 列)
2025-09-02 13:29:59.327 | INFO     | src.modules.data_import.excel_importer:_import_normal_file:362 | 导入完成: 1行 x 16列
2025-09-02 13:29:59.327 | INFO     | src.modules.data_import.excel_importer:import_data:301 | [修复标识] 数据导入最终完成: 1行 × 16列
2025-09-02 13:29:59 - src.gui.unified_data_import_window - INFO - 从Excel文件获取字段: 16 个
2025-09-02 13:29:59 - src.gui.unified_data_import_window - INFO - 加载Excel字段: 16 个字段, 表类型: 
2025-09-02 13:29:59 - src.gui.unified_data_import_window - INFO - 字段映射表格创建完成: 16 行
2025-09-02 13:29:59 - src.gui.unified_data_import_window - INFO - 更新预览验证内容: 离休人员工资表
2025-09-02 13:29:59.445 | INFO     | src.utils.log_config:log_file_operation:336 | 文件C:\test\salary_changes\salary_changes\data\工资表\2025年5月份正式工工资（报财务)  最终版.xls: read
2025-09-02 13:29:59.560 | INFO     | src.modules.data_import.excel_importer:_clean_data:602 | [修复标识] Excel导入器 _clean_data 方法 - 保留所有列结构修复已执行
2025-09-02 13:29:59.562 | INFO     | src.modules.data_import.excel_importer:_clean_data:613 | 数据清理完成: 保留所有 16 列 (原始 16 列)
2025-09-02 13:29:59.564 | INFO     | src.modules.data_import.excel_importer:_filter_invalid_data:742 | 数据质量检查: 已过滤1条姓名为空的记录
2025-09-02 13:29:59.566 | INFO     | src.modules.data_import.excel_importer:_filter_invalid_data:785 | 数据过滤完成: 原始3条记录，过滤1条无效记录，有效记录2条
2025-09-02 13:29:59 - src.gui.unified_data_import_window - INFO - 预览数据加载完成（已应用格式化）: 离休人员工资表 - 2 行
2025-09-02 13:29:59 - src.gui.unified_data_import_window - INFO - 成功更新预览数据: 2 行, 16 列
2025-09-02 13:29:59 - src.gui.unified_data_import_window - INFO - 已加载Sheet '离休人员工资表' 的预览数据: 2 行
2025-09-02 13:30:00 - src.gui.unified_data_import_window - INFO - 选中Sheet: A岗职工
2025-09-02 13:30:00 - src.gui.unified_data_import_window - INFO - 当前Sheet变化: A岗职工
2025-09-02 13:30:00.649 | INFO     | src.gui.widgets.data_processing_widget:_load_template_list:732 | 加载了 10 个模板
2025-09-02 13:30:00.649 | INFO     | src.gui.widgets.data_processing_widget:update_for_sheet:416 | 数据处理配置已切换到Sheet: A岗职工
2025-09-02 13:30:00 - src.gui.unified_data_import_window - INFO - 更新字段映射配置: A岗职工
2025-09-02 13:30:00.651 | INFO     | src.modules.data_import.excel_importer:validate_file:115 | 文件验证成功: 2025年5月份正式工工资（报财务)  最终版.xls (1.17MB)
2025-09-02 13:30:00.652 | INFO     | src.modules.data_import.excel_importer:import_data:274 | 开始导入Excel文件: 2025年5月份正式工工资（报财务)  最终版.xls
2025-09-02 13:30:00.652 | INFO     | src.utils.log_config:log_file_operation:336 | 文件C:\test\salary_changes\salary_changes\data\工资表\2025年5月份正式工工资（报财务)  最终版.xls: read
2025-09-02 13:30:00.761 | INFO     | src.modules.data_import.excel_importer:_clean_data:602 | [修复标识] Excel导入器 _clean_data 方法 - 保留所有列结构修复已执行
2025-09-02 13:30:00.764 | INFO     | src.modules.data_import.excel_importer:_clean_data:613 | 数据清理完成: 保留所有 21 列 (原始 21 列)
2025-09-02 13:30:00.764 | INFO     | src.modules.data_import.excel_importer:_import_normal_file:362 | 导入完成: 1行 x 21列
2025-09-02 13:30:00.768 | INFO     | src.modules.data_import.excel_importer:import_data:301 | [修复标识] 数据导入最终完成: 1行 × 21列
2025-09-02 13:30:00 - src.gui.unified_data_import_window - INFO - 从Excel文件获取字段: 21 个
2025-09-02 13:30:00 - src.gui.unified_data_import_window - INFO - 加载Excel字段: 21 个字段, 表类型: 
2025-09-02 13:30:00 - src.gui.unified_data_import_window - INFO - 字段映射表格创建完成: 21 行
2025-09-02 13:30:00 - src.gui.unified_data_import_window - INFO - 更新预览验证内容: A岗职工
2025-09-02 13:30:00.900 | INFO     | src.utils.log_config:log_file_operation:336 | 文件C:\test\salary_changes\salary_changes\data\工资表\2025年5月份正式工工资（报财务)  最终版.xls: read
2025-09-02 13:30:01.016 | INFO     | src.modules.data_import.excel_importer:_clean_data:602 | [修复标识] Excel导入器 _clean_data 方法 - 保留所有列结构修复已执行
2025-09-02 13:30:01.018 | INFO     | src.modules.data_import.excel_importer:_clean_data:613 | 数据清理完成: 保留所有 21 列 (原始 21 列)
2025-09-02 13:30:01.018 | INFO     | src.modules.data_import.excel_importer:_filter_invalid_data:742 | 数据质量检查: 已过滤1条姓名为空的记录
2025-09-02 13:30:01.024 | INFO     | src.modules.data_import.excel_importer:_filter_invalid_data:785 | 数据过滤完成: 原始63条记录，过滤1条无效记录，有效记录62条
2025-09-02 13:30:01 - src.gui.unified_data_import_window - INFO - 预览数据加载完成（已应用格式化）: A岗职工 - 62 行
2025-09-02 13:30:01 - src.gui.unified_data_import_window - INFO - 成功更新预览数据: 62 行, 21 列
2025-09-02 13:30:01 - src.gui.unified_data_import_window - INFO - 已加载Sheet 'A岗职工' 的预览数据: 62 行
2025-09-02 13:30:04 - src.gui.unified_data_import_window - INFO - 选项卡切换到: 🔗 字段映射 (索引: 0)
2025-09-02 13:30:04.850 | INFO     | src.modules.data_import.config_sync_manager:save_mapping:447 | 字段映射保存成功: mapping_config_20250902_133004
2025-09-02 13:30:04 - src.gui.unified_data_import_window - INFO - 字段映射实时保存成功: mapping_config_20250902_133004
2025-09-02 13:30:07 - src.gui.unified_data_import_window - INFO - 选中Sheet: 离休人员工资表
2025-09-02 13:30:07 - src.gui.unified_data_import_window - INFO - 当前Sheet变化: 离休人员工资表
2025-09-02 13:30:07.378 | INFO     | src.gui.widgets.data_processing_widget:_load_template_list:732 | 加载了 10 个模板
2025-09-02 13:30:07.385 | INFO     | src.gui.widgets.data_processing_widget:update_for_sheet:416 | 数据处理配置已切换到Sheet: 离休人员工资表
2025-09-02 13:30:07 - src.gui.unified_data_import_window - INFO - 更新字段映射配置: 离休人员工资表
2025-09-02 13:30:07.387 | INFO     | src.modules.data_import.excel_importer:validate_file:115 | 文件验证成功: 2025年5月份正式工工资（报财务)  最终版.xls (1.17MB)
2025-09-02 13:30:07.387 | INFO     | src.modules.data_import.excel_importer:import_data:274 | 开始导入Excel文件: 2025年5月份正式工工资（报财务)  最终版.xls
2025-09-02 13:30:07.387 | INFO     | src.utils.log_config:log_file_operation:336 | 文件C:\test\salary_changes\salary_changes\data\工资表\2025年5月份正式工工资（报财务)  最终版.xls: read
2025-09-02 13:30:07.504 | INFO     | src.modules.data_import.excel_importer:_import_normal_file:337 | [修复标识] Excel读取完成: 16列 (列过滤: 否)
2025-09-02 13:30:07.511 | INFO     | src.modules.data_import.excel_importer:_clean_data:602 | [修复标识] Excel导入器 _clean_data 方法 - 保留所有列结构修复已执行
2025-09-02 13:30:07.512 | INFO     | src.modules.data_import.excel_importer:_clean_data:613 | 数据清理完成: 保留所有 16 列 (原始 16 列)
2025-09-02 13:30:07.512 | INFO     | src.modules.data_import.excel_importer:_import_normal_file:362 | 导入完成: 1行 x 16列
2025-09-02 13:30:07.516 | INFO     | src.modules.data_import.excel_importer:import_data:301 | [修复标识] 数据导入最终完成: 1行 × 16列
2025-09-02 13:30:07 - src.gui.unified_data_import_window - INFO - 从Excel文件获取字段: 16 个
2025-09-02 13:30:07 - src.gui.unified_data_import_window - INFO - 加载Excel字段: 16 个字段, 表类型: 
2025-09-02 13:30:07 - src.gui.unified_data_import_window - INFO - 字段映射表格创建完成: 16 行
2025-09-02 13:30:07 - src.gui.unified_data_import_window - INFO - 更新预览验证内容: 离休人员工资表
2025-09-02 13:30:07.628 | INFO     | src.utils.log_config:log_file_operation:336 | 文件C:\test\salary_changes\salary_changes\data\工资表\2025年5月份正式工工资（报财务)  最终版.xls: read
2025-09-02 13:30:07.737 | INFO     | src.modules.data_import.excel_importer:_clean_data:602 | [修复标识] Excel导入器 _clean_data 方法 - 保留所有列结构修复已执行
2025-09-02 13:30:07.745 | INFO     | src.modules.data_import.excel_importer:_clean_data:613 | 数据清理完成: 保留所有 16 列 (原始 16 列)
2025-09-02 13:30:07.745 | INFO     | src.modules.data_import.excel_importer:_filter_invalid_data:742 | 数据质量检查: 已过滤1条姓名为空的记录
2025-09-02 13:30:07.748 | INFO     | src.modules.data_import.excel_importer:_filter_invalid_data:785 | 数据过滤完成: 原始3条记录，过滤1条无效记录，有效记录2条
2025-09-02 13:30:07 - src.gui.unified_data_import_window - INFO - 预览数据加载完成（已应用格式化）: 离休人员工资表 - 2 行
2025-09-02 13:30:07 - src.gui.unified_data_import_window - INFO - 成功更新预览数据: 2 行, 16 列
2025-09-02 13:30:07 - src.gui.unified_data_import_window - INFO - 已加载Sheet '离休人员工资表' 的预览数据: 2 行
2025-09-02 13:30:10 - src.gui.unified_data_import_window - INFO - 选项卡切换到: 👁️ 预览验证 (索引: 2)
2025-09-02 13:30:10.462 | INFO     | src.modules.data_import.config_sync_manager:save_mapping:447 | 字段映射保存成功: mapping_config_20250902_133010
2025-09-02 13:30:10 - src.gui.unified_data_import_window - INFO - 字段映射实时保存成功: mapping_config_20250902_133010
2025-09-02 13:30:10.463 | WARNING  | src.modules.data_import.formatting_engine:apply:93 | 数值格式化失败: could not convert string to float: '王太西'
2025-09-02 13:30:10.465 | WARNING  | src.modules.data_import.formatting_engine:apply:93 | 数值格式化失败: could not convert string to float: '离休'
2025-09-02 13:30:10.466 | WARNING  | src.modules.data_import.formatting_engine:apply:93 | 数值格式化失败: could not convert string to float: '赵君励'
2025-09-02 13:30:10.470 | WARNING  | src.modules.data_import.formatting_engine:apply:93 | 数值格式化失败: could not convert string to float: '离休'
2025-09-02 13:30:10 - src.gui.unified_data_import_window - INFO - 预览数据加载完成（已应用格式化）: 离休人员工资表 - 2 行
2025-09-02 13:30:10 - src.gui.unified_data_import_window - INFO - 切换到预览选项卡，已刷新格式化
2025-09-02 13:30:14 - src.gui.unified_data_import_window - INFO - 选项卡切换到: 🔗 字段映射 (索引: 0)
2025-09-02 13:30:14.462 | INFO     | src.modules.data_import.config_sync_manager:save_mapping:447 | 字段映射保存成功: mapping_config_20250902_133014
2025-09-02 13:30:14 - src.gui.unified_data_import_window - INFO - 字段映射实时保存成功: mapping_config_20250902_133014
2025-09-02 13:30:16 - src.gui.unified_data_import_window - INFO - 开始预览Sheet: 离休人员工资表
2025-09-02 13:30:16.430 | INFO     | src.modules.data_import.excel_importer:validate_file:115 | 文件验证成功: 2025年5月份正式工工资（报财务)  最终版.xls (1.17MB)
2025-09-02 13:30:16.433 | INFO     | src.modules.data_import.excel_importer:import_data:274 | 开始导入Excel文件: 2025年5月份正式工工资（报财务)  最终版.xls
2025-09-02 13:30:16.433 | INFO     | src.utils.log_config:log_file_operation:336 | 文件C:\test\salary_changes\salary_changes\data\工资表\2025年5月份正式工工资（报财务)  最终版.xls: read
2025-09-02 13:30:16.609 | INFO     | src.modules.data_import.excel_importer:_import_normal_file:337 | [修复标识] Excel读取完成: 16列 (列过滤: 否)
2025-09-02 13:30:16.612 | INFO     | src.modules.data_import.excel_importer:_clean_data:602 | [修复标识] Excel导入器 _clean_data 方法 - 保留所有列结构修复已执行
2025-09-02 13:30:16.616 | INFO     | src.modules.data_import.excel_importer:_clean_data:613 | 数据清理完成: 保留所有 16 列 (原始 16 列)
2025-09-02 13:30:16.617 | INFO     | src.modules.data_import.excel_importer:_import_normal_file:362 | 导入完成: 3行 x 16列
2025-09-02 13:30:16.621 | INFO     | src.modules.data_import.excel_importer:_filter_invalid_data:742 | 数据质量检查: 已过滤1条姓名为空的记录
2025-09-02 13:30:16.624 | INFO     | src.modules.data_import.excel_importer:_filter_invalid_data:785 | 数据过滤完成: 原始3条记录，过滤1条无效记录，有效记录2条
2025-09-02 13:30:16.626 | INFO     | src.modules.data_import.excel_importer:import_data:301 | [修复标识] 数据导入最终完成: 2行 × 16列
2025-09-02 13:30:16 - src.gui.unified_data_import_window - INFO - 成功读取预览数据: 2 行, 16 列
2025-09-02 13:30:16 - src.gui.unified_data_import_window - INFO - 选项卡切换到: 👁️ 预览验证 (索引: 2)
2025-09-02 13:30:16.647 | INFO     | src.modules.data_import.config_sync_manager:save_mapping:447 | 字段映射保存成功: mapping_config_20250902_133016
2025-09-02 13:30:16 - src.gui.unified_data_import_window - INFO - 字段映射实时保存成功: mapping_config_20250902_133016
2025-09-02 13:30:16.651 | WARNING  | src.modules.data_import.formatting_engine:apply:93 | 数值格式化失败: could not convert string to float: '王太西'
2025-09-02 13:30:16.652 | WARNING  | src.modules.data_import.formatting_engine:apply:93 | 数值格式化失败: could not convert string to float: '离休'
2025-09-02 13:30:16.653 | WARNING  | src.modules.data_import.formatting_engine:apply:93 | 数值格式化失败: could not convert string to float: '赵君励'
2025-09-02 13:30:16.654 | WARNING  | src.modules.data_import.formatting_engine:apply:93 | 数值格式化失败: could not convert string to float: '离休'
2025-09-02 13:30:16 - src.gui.unified_data_import_window - INFO - 预览数据加载完成（已应用格式化）: 离休人员工资表 - 2 行
2025-09-02 13:30:16 - src.gui.unified_data_import_window - INFO - 切换到预览选项卡，已刷新格式化
2025-09-02 13:30:16.657 | WARNING  | src.modules.data_import.formatting_engine:apply:93 | 数值格式化失败: could not convert string to float: '王太西'
2025-09-02 13:30:16.657 | WARNING  | src.modules.data_import.formatting_engine:apply:93 | 数值格式化失败: could not convert string to float: '离休'
2025-09-02 13:30:16.658 | WARNING  | src.modules.data_import.formatting_engine:apply:93 | 数值格式化失败: could not convert string to float: '赵君励'
2025-09-02 13:30:16.659 | WARNING  | src.modules.data_import.formatting_engine:apply:93 | 数值格式化失败: could not convert string to float: '离休'
2025-09-02 13:30:16 - src.gui.unified_data_import_window - INFO - 预览数据加载完成（已应用格式化）: 离休人员工资表 - 2 行
2025-09-02 13:30:19 - src.gui.unified_data_import_window - INFO - 选项卡切换到: 🔗 字段映射 (索引: 0)
2025-09-02 13:30:19.623 | INFO     | src.modules.data_import.config_sync_manager:save_mapping:447 | 字段映射保存成功: mapping_config_20250902_133019
2025-09-02 13:30:19 - src.gui.unified_data_import_window - INFO - 字段映射实时保存成功: mapping_config_20250902_133019
2025-09-02 13:30:22.227 | INFO     | src.modules.data_import.config_sync_manager:save_mapping:447 | 字段映射保存成功: mapping_config_20250902_133022
2025-09-02 13:30:22 - src.gui.unified_data_import_window - INFO - 字段映射实时保存成功: mapping_config_20250902_133022
2025-09-02 13:30:24.261 | INFO     | src.modules.data_import.config_sync_manager:save_mapping:447 | 字段映射保存成功: mapping_config_20250902_133024
2025-09-02 13:30:24 - src.gui.unified_data_import_window - INFO - 字段映射实时保存成功: mapping_config_20250902_133024
2025-09-02 13:30:26.101 | INFO     | src.modules.data_import.config_sync_manager:save_mapping:447 | 字段映射保存成功: mapping_config_20250902_133026
2025-09-02 13:30:26 - src.gui.unified_data_import_window - INFO - 字段映射实时保存成功: mapping_config_20250902_133026
2025-09-02 13:30:28.404 | INFO     | src.modules.data_import.config_sync_manager:save_mapping:447 | 字段映射保存成功: mapping_config_20250902_133028
2025-09-02 13:30:28 - src.gui.unified_data_import_window - INFO - 字段映射实时保存成功: mapping_config_20250902_133028
2025-09-02 13:30:44 - src.gui.unified_data_import_window - INFO - 选项卡切换到: 👁️ 预览验证 (索引: 2)
2025-09-02 13:30:44.069 | INFO     | src.modules.data_import.config_sync_manager:save_mapping:447 | 字段映射保存成功: mapping_config_20250902_133044
2025-09-02 13:30:44 - src.gui.unified_data_import_window - INFO - 字段映射实时保存成功: mapping_config_20250902_133044
2025-09-02 13:30:44 - src.gui.unified_data_import_window - INFO - 预览数据加载完成（已应用格式化）: 离休人员工资表 - 2 行
2025-09-02 13:30:44 - src.gui.unified_data_import_window - INFO - 切换到预览选项卡，已刷新格式化
2025-09-02 13:30:52 - src.gui.unified_data_import_window - INFO - 选项卡切换到: 🔗 字段映射 (索引: 0)
2025-09-02 13:30:52.416 | INFO     | src.modules.data_import.config_sync_manager:save_mapping:447 | 字段映射保存成功: mapping_config_20250902_133052
2025-09-02 13:30:52 - src.gui.unified_data_import_window - INFO - 字段映射实时保存成功: mapping_config_20250902_133052
2025-09-02 13:31:06 - src.gui.unified_data_import_window - INFO - 开始预览Sheet: 离休人员工资表
2025-09-02 13:31:06.398 | INFO     | src.modules.data_import.excel_importer:validate_file:115 | 文件验证成功: 2025年5月份正式工工资（报财务)  最终版.xls (1.17MB)
2025-09-02 13:31:06.401 | INFO     | src.modules.data_import.excel_importer:import_data:274 | 开始导入Excel文件: 2025年5月份正式工工资（报财务)  最终版.xls
2025-09-02 13:31:06.401 | INFO     | src.utils.log_config:log_file_operation:336 | 文件C:\test\salary_changes\salary_changes\data\工资表\2025年5月份正式工工资（报财务)  最终版.xls: read
2025-09-02 13:31:06.515 | INFO     | src.modules.data_import.excel_importer:_import_normal_file:337 | [修复标识] Excel读取完成: 16列 (列过滤: 否)
2025-09-02 13:31:06.518 | INFO     | src.modules.data_import.excel_importer:_clean_data:602 | [修复标识] Excel导入器 _clean_data 方法 - 保留所有列结构修复已执行
2025-09-02 13:31:06.520 | INFO     | src.modules.data_import.excel_importer:_clean_data:613 | 数据清理完成: 保留所有 16 列 (原始 16 列)
2025-09-02 13:31:06.521 | INFO     | src.modules.data_import.excel_importer:_import_normal_file:362 | 导入完成: 3行 x 16列
2025-09-02 13:31:06.523 | INFO     | src.modules.data_import.excel_importer:_filter_invalid_data:742 | 数据质量检查: 已过滤1条姓名为空的记录
2025-09-02 13:31:06.524 | INFO     | src.modules.data_import.excel_importer:_filter_invalid_data:785 | 数据过滤完成: 原始3条记录，过滤1条无效记录，有效记录2条
2025-09-02 13:31:06.526 | INFO     | src.modules.data_import.excel_importer:import_data:301 | [修复标识] 数据导入最终完成: 2行 × 16列
2025-09-02 13:31:06 - src.gui.unified_data_import_window - INFO - 成功读取预览数据: 2 行, 16 列
2025-09-02 13:31:06 - src.gui.unified_data_import_window - INFO - 选项卡切换到: 👁️ 预览验证 (索引: 2)
2025-09-02 13:31:06.553 | INFO     | src.modules.data_import.config_sync_manager:save_mapping:447 | 字段映射保存成功: mapping_config_20250902_133106
2025-09-02 13:31:06 - src.gui.unified_data_import_window - INFO - 字段映射实时保存成功: mapping_config_20250902_133106
2025-09-02 13:31:06 - src.gui.unified_data_import_window - INFO - 预览数据加载完成（已应用格式化）: 离休人员工资表 - 2 行
2025-09-02 13:31:06 - src.gui.unified_data_import_window - INFO - 切换到预览选项卡，已刷新格式化
2025-09-02 13:31:06 - src.gui.unified_data_import_window - INFO - 预览数据加载完成（已应用格式化）: 离休人员工资表 - 2 行
2025-09-02 13:31:12 - src.gui.unified_data_import_window - INFO - 选项卡切换到: 🔗 字段映射 (索引: 0)
2025-09-02 13:31:12.037 | INFO     | src.modules.data_import.config_sync_manager:save_mapping:447 | 字段映射保存成功: mapping_config_20250902_133112
2025-09-02 13:31:12 - src.gui.unified_data_import_window - INFO - 字段映射实时保存成功: mapping_config_20250902_133112
2025-09-02 13:31:13 - src.gui.unified_data_import_window - INFO - 选项卡切换到: 📝 字段类型 (索引: 3)
2025-09-02 13:31:13.314 | INFO     | src.modules.data_import.config_sync_manager:save_mapping:447 | 字段映射保存成功: mapping_config_20250902_133113
2025-09-02 13:31:13 - src.gui.unified_data_import_window - INFO - 字段映射实时保存成功: mapping_config_20250902_133113
2025-09-02 13:31:17.852 | INFO     | src.modules.data_import.field_type_manager:__init__:41 | 字段类型管理器初始化完成，存储目录: C:\test\salary_changes\salary_changes\state\field_types
2025-09-02 13:33:10.503 | INFO     | src.modules.data_import.field_type_manager:_save_custom_field_types:70 | 自定义字段类型已保存
2025-09-02 13:33:10.503 | INFO     | src.modules.data_import.field_type_manager:create_field_type:119 | 创建自定义字段类型: person_code - 人员代码
2025-09-02 13:33:10 - src.gui.unified_data_import_window - INFO - 字段类型 person_code 已变更，刷新相关配置
2025-09-02 13:33:10 - src.gui.widgets.field_type_config_widget - INFO - 字段类型 person_code 创建完成
2025-09-02 13:33:12 - src.gui.widgets.field_type_config_widget - INFO - 已加载 10 个内置类型和 0 个自定义类型
2025-09-02 13:33:12 - src.gui.widgets.field_type_config_widget - INFO - 新字段类型创建成功
2025-09-02 13:33:37 - src.gui.unified_data_import_window - INFO - 选项卡切换到: 🔗 字段映射 (索引: 0)
2025-09-02 13:33:37.576 | INFO     | src.modules.data_import.config_sync_manager:save_mapping:447 | 字段映射保存成功: mapping_config_20250902_133337
2025-09-02 13:33:37 - src.gui.unified_data_import_window - INFO - 字段映射实时保存成功: mapping_config_20250902_133337
2025-09-02 13:33:47 - src.gui.unified_data_import_window - INFO - 开始预览Sheet: 离休人员工资表
2025-09-02 13:33:47.847 | INFO     | src.modules.data_import.excel_importer:validate_file:115 | 文件验证成功: 2025年5月份正式工工资（报财务)  最终版.xls (1.17MB)
2025-09-02 13:33:47.848 | INFO     | src.modules.data_import.excel_importer:import_data:274 | 开始导入Excel文件: 2025年5月份正式工工资（报财务)  最终版.xls
2025-09-02 13:33:47.849 | INFO     | src.utils.log_config:log_file_operation:336 | 文件C:\test\salary_changes\salary_changes\data\工资表\2025年5月份正式工工资（报财务)  最终版.xls: read
2025-09-02 13:33:47.954 | INFO     | src.modules.data_import.excel_importer:_import_normal_file:337 | [修复标识] Excel读取完成: 16列 (列过滤: 否)
2025-09-02 13:33:47.960 | INFO     | src.modules.data_import.excel_importer:_clean_data:602 | [修复标识] Excel导入器 _clean_data 方法 - 保留所有列结构修复已执行
2025-09-02 13:33:47.961 | INFO     | src.modules.data_import.excel_importer:_clean_data:613 | 数据清理完成: 保留所有 16 列 (原始 16 列)
2025-09-02 13:33:47.962 | INFO     | src.modules.data_import.excel_importer:_import_normal_file:362 | 导入完成: 3行 x 16列
2025-09-02 13:33:47.965 | INFO     | src.modules.data_import.excel_importer:_filter_invalid_data:742 | 数据质量检查: 已过滤1条姓名为空的记录
2025-09-02 13:33:47.965 | INFO     | src.modules.data_import.excel_importer:_filter_invalid_data:785 | 数据过滤完成: 原始3条记录，过滤1条无效记录，有效记录2条
2025-09-02 13:33:47.972 | INFO     | src.modules.data_import.excel_importer:import_data:301 | [修复标识] 数据导入最终完成: 2行 × 16列
2025-09-02 13:33:47 - src.gui.unified_data_import_window - INFO - 成功读取预览数据: 2 行, 16 列
2025-09-02 13:33:47 - src.gui.unified_data_import_window - INFO - 选项卡切换到: 👁️ 预览验证 (索引: 2)
2025-09-02 13:33:47.986 | INFO     | src.modules.data_import.config_sync_manager:save_mapping:447 | 字段映射保存成功: mapping_config_20250902_133347
2025-09-02 13:33:47 - src.gui.unified_data_import_window - INFO - 字段映射实时保存成功: mapping_config_20250902_133347
2025-09-02 13:33:47 - src.gui.unified_data_import_window - INFO - 预览数据加载完成（已应用格式化）: 离休人员工资表 - 2 行
2025-09-02 13:33:47 - src.gui.unified_data_import_window - INFO - 切换到预览选项卡，已刷新格式化
2025-09-02 13:33:48 - src.gui.unified_data_import_window - INFO - 预览数据加载完成（已应用格式化）: 离休人员工资表 - 2 行
2025-09-02 13:33:54 - src.gui.unified_data_import_window - INFO - 选项卡切换到: 🧹 数据处理 (索引: 1)
2025-09-02 13:33:54.898 | INFO     | src.modules.data_import.config_sync_manager:save_mapping:447 | 字段映射保存成功: mapping_config_20250902_133354
2025-09-02 13:33:54 - src.gui.unified_data_import_window - INFO - 字段映射实时保存成功: mapping_config_20250902_133354
2025-09-02 13:33:57 - src.gui.unified_data_import_window - INFO - 选项卡切换到: 🔗 字段映射 (索引: 0)
2025-09-02 13:33:57.248 | INFO     | src.modules.data_import.config_sync_manager:save_mapping:447 | 字段映射保存成功: mapping_config_20250902_133357
2025-09-02 13:33:57 - src.gui.unified_data_import_window - INFO - 字段映射实时保存成功: mapping_config_20250902_133357
2025-09-02 13:33:59 - src.gui.unified_data_import_window - INFO - 选项卡切换到: 👁️ 预览验证 (索引: 2)
2025-09-02 13:33:59.976 | INFO     | src.modules.data_import.config_sync_manager:save_mapping:447 | 字段映射保存成功: mapping_config_20250902_133359
2025-09-02 13:33:59 - src.gui.unified_data_import_window - INFO - 字段映射实时保存成功: mapping_config_20250902_133359
2025-09-02 13:33:59 - src.gui.unified_data_import_window - INFO - 预览数据加载完成（已应用格式化）: 离休人员工资表 - 2 行
2025-09-02 13:33:59 - src.gui.unified_data_import_window - INFO - 切换到预览选项卡，已刷新格式化
2025-09-02 13:34:04 - src.gui.unified_data_import_window - INFO - 选项卡切换到: 🔗 字段映射 (索引: 0)
2025-09-02 13:34:04.751 | INFO     | src.modules.data_import.config_sync_manager:save_mapping:447 | 字段映射保存成功: mapping_config_20250902_133404
2025-09-02 13:34:04 - src.gui.unified_data_import_window - INFO - 字段映射实时保存成功: mapping_config_20250902_133404
2025-09-02 13:34:05 - src.gui.unified_data_import_window - INFO - 选中Sheet: A岗职工
2025-09-02 13:34:05 - src.gui.unified_data_import_window - INFO - 当前Sheet变化: A岗职工
2025-09-02 13:34:05.726 | INFO     | src.gui.widgets.data_processing_widget:_load_template_list:732 | 加载了 10 个模板
2025-09-02 13:34:05.726 | INFO     | src.gui.widgets.data_processing_widget:update_for_sheet:416 | 数据处理配置已切换到Sheet: A岗职工
2025-09-02 13:34:05 - src.gui.unified_data_import_window - INFO - 更新字段映射配置: A岗职工
2025-09-02 13:34:05.726 | INFO     | src.modules.data_import.excel_importer:validate_file:115 | 文件验证成功: 2025年5月份正式工工资（报财务)  最终版.xls (1.17MB)
2025-09-02 13:34:05.726 | INFO     | src.modules.data_import.excel_importer:import_data:274 | 开始导入Excel文件: 2025年5月份正式工工资（报财务)  最终版.xls
2025-09-02 13:34:05.726 | INFO     | src.utils.log_config:log_file_operation:336 | 文件C:\test\salary_changes\salary_changes\data\工资表\2025年5月份正式工工资（报财务)  最终版.xls: read
2025-09-02 13:34:05.844 | INFO     | src.modules.data_import.excel_importer:_import_normal_file:337 | [修复标识] Excel读取完成: 21列 (列过滤: 否)
2025-09-02 13:34:05.849 | INFO     | src.modules.data_import.excel_importer:_clean_data:602 | [修复标识] Excel导入器 _clean_data 方法 - 保留所有列结构修复已执行
2025-09-02 13:34:05.852 | INFO     | src.modules.data_import.excel_importer:_clean_data:613 | 数据清理完成: 保留所有 21 列 (原始 21 列)
2025-09-02 13:34:05.852 | INFO     | src.modules.data_import.excel_importer:_import_normal_file:362 | 导入完成: 1行 x 21列
2025-09-02 13:34:05.854 | INFO     | src.modules.data_import.excel_importer:import_data:301 | [修复标识] 数据导入最终完成: 1行 × 21列
2025-09-02 13:34:05 - src.gui.unified_data_import_window - INFO - 从Excel文件获取字段: 21 个
2025-09-02 13:34:05 - src.gui.unified_data_import_window - INFO - 加载Excel字段: 21 个字段, 表类型: 
2025-09-02 13:34:05 - src.gui.unified_data_import_window - INFO - 字段映射表格创建完成: 21 行
2025-09-02 13:34:05 - src.gui.unified_data_import_window - INFO - 更新预览验证内容: A岗职工
2025-09-02 13:34:05.976 | INFO     | src.utils.log_config:log_file_operation:336 | 文件C:\test\salary_changes\salary_changes\data\工资表\2025年5月份正式工工资（报财务)  最终版.xls: read
2025-09-02 13:34:06.154 | INFO     | src.modules.data_import.excel_importer:_clean_data:602 | [修复标识] Excel导入器 _clean_data 方法 - 保留所有列结构修复已执行
2025-09-02 13:34:06.158 | INFO     | src.modules.data_import.excel_importer:_clean_data:613 | 数据清理完成: 保留所有 21 列 (原始 21 列)
2025-09-02 13:34:06.161 | INFO     | src.modules.data_import.excel_importer:_filter_invalid_data:742 | 数据质量检查: 已过滤1条姓名为空的记录
2025-09-02 13:34:06.163 | INFO     | src.modules.data_import.excel_importer:_filter_invalid_data:785 | 数据过滤完成: 原始63条记录，过滤1条无效记录，有效记录62条
2025-09-02 13:34:06 - src.gui.unified_data_import_window - INFO - 预览数据加载完成（已应用格式化）: A岗职工 - 62 行
2025-09-02 13:34:06 - src.gui.unified_data_import_window - INFO - 成功更新预览数据: 62 行, 21 列
2025-09-02 13:34:06 - src.gui.unified_data_import_window - INFO - 已加载Sheet 'A岗职工' 的预览数据: 62 行
2025-09-02 13:34:07 - src.gui.unified_data_import_window - INFO - 选中Sheet: 离休人员工资表
2025-09-02 13:34:07 - src.gui.unified_data_import_window - INFO - 当前Sheet变化: 离休人员工资表
2025-09-02 13:34:07.535 | INFO     | src.gui.widgets.data_processing_widget:_load_template_list:732 | 加载了 10 个模板
2025-09-02 13:34:07.535 | INFO     | src.gui.widgets.data_processing_widget:update_for_sheet:416 | 数据处理配置已切换到Sheet: 离休人员工资表
2025-09-02 13:34:07 - src.gui.unified_data_import_window - INFO - 更新字段映射配置: 离休人员工资表
2025-09-02 13:34:07.535 | INFO     | src.modules.data_import.excel_importer:validate_file:115 | 文件验证成功: 2025年5月份正式工工资（报财务)  最终版.xls (1.17MB)
2025-09-02 13:34:07.535 | INFO     | src.modules.data_import.excel_importer:import_data:274 | 开始导入Excel文件: 2025年5月份正式工工资（报财务)  最终版.xls
2025-09-02 13:34:07.535 | INFO     | src.utils.log_config:log_file_operation:336 | 文件C:\test\salary_changes\salary_changes\data\工资表\2025年5月份正式工工资（报财务)  最终版.xls: read
2025-09-02 13:34:07.648 | INFO     | src.modules.data_import.excel_importer:_clean_data:602 | [修复标识] Excel导入器 _clean_data 方法 - 保留所有列结构修复已执行
2025-09-02 13:34:07.650 | INFO     | src.modules.data_import.excel_importer:_clean_data:613 | 数据清理完成: 保留所有 16 列 (原始 16 列)
2025-09-02 13:34:07.650 | INFO     | src.modules.data_import.excel_importer:_import_normal_file:362 | 导入完成: 1行 x 16列
2025-09-02 13:34:07.654 | INFO     | src.modules.data_import.excel_importer:import_data:301 | [修复标识] 数据导入最终完成: 1行 × 16列
2025-09-02 13:34:07 - src.gui.unified_data_import_window - INFO - 从Excel文件获取字段: 16 个
2025-09-02 13:34:07 - src.gui.unified_data_import_window - INFO - 加载Excel字段: 16 个字段, 表类型: 
2025-09-02 13:34:07 - src.gui.unified_data_import_window - INFO - 字段映射表格创建完成: 16 行
2025-09-02 13:34:07 - src.gui.unified_data_import_window - INFO - 更新预览验证内容: 离休人员工资表
2025-09-02 13:34:07.759 | INFO     | src.utils.log_config:log_file_operation:336 | 文件C:\test\salary_changes\salary_changes\data\工资表\2025年5月份正式工工资（报财务)  最终版.xls: read
2025-09-02 13:34:07.859 | INFO     | src.modules.data_import.excel_importer:_import_normal_file:337 | [修复标识] Excel读取完成: 16列 (列过滤: 否)
2025-09-02 13:34:07.863 | INFO     | src.modules.data_import.excel_importer:_clean_data:602 | [修复标识] Excel导入器 _clean_data 方法 - 保留所有列结构修复已执行
2025-09-02 13:34:07.865 | INFO     | src.modules.data_import.excel_importer:_clean_data:613 | 数据清理完成: 保留所有 16 列 (原始 16 列)
2025-09-02 13:34:07.867 | INFO     | src.modules.data_import.excel_importer:_filter_invalid_data:742 | 数据质量检查: 已过滤1条姓名为空的记录
2025-09-02 13:34:07.867 | INFO     | src.modules.data_import.excel_importer:_filter_invalid_data:785 | 数据过滤完成: 原始3条记录，过滤1条无效记录，有效记录2条
2025-09-02 13:34:07 - src.gui.unified_data_import_window - INFO - 预览数据加载完成（已应用格式化）: 离休人员工资表 - 2 行
2025-09-02 13:34:07 - src.gui.unified_data_import_window - INFO - 成功更新预览数据: 2 行, 16 列
2025-09-02 13:34:07 - src.gui.unified_data_import_window - INFO - 已加载Sheet '离休人员工资表' 的预览数据: 2 行
2025-09-02 13:34:13.011 | INFO     | src.gui.prototype.prototype_main_window:_show_unified_import_dialog:6005 | 用户取消了数据导入
2025-09-02 13:34:15.276 | INFO     | __main__:main:519 | 应用程序正常退出

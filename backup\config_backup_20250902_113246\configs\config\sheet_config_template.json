{"import_strategy": "merge_to_single_table", "target_table_template": "salary_data", "sheet_mappings": {"基本信息": {"enabled": true, "field_mappings": {"工号": "employee_id", "姓名": "employee_name", "员工姓名": "employee_name", "身份证号": "id_card", "部门": "department", "职位": "position", "岗位": "position"}, "required_fields": ["employee_id", "employee_name"], "data_type": "salary_data"}, "工资明细": {"enabled": true, "field_mappings": {"工号": "employee_id", "基本工资": "basic_salary", "基础工资": "basic_salary", "绩效奖金": "performance_bonus", "绩效": "performance_bonus", "加班费": "overtime_pay", "总工资": "total_salary", "应发工资": "total_salary"}, "required_fields": ["employee_id"], "data_type": "salary_detail"}, "津贴补贴": {"enabled": true, "field_mappings": {"工号": "employee_id", "津贴": "allowance", "补贴": "allowance", "交通补贴": "transportation_allowance", "餐补": "meal_allowance", "通讯补贴": "communication_allowance"}, "required_fields": ["employee_id"], "data_type": "allowance_detail"}, "扣款明细": {"enabled": true, "field_mappings": {"工号": "employee_id", "扣款": "deduction", "社保扣款": "social_security_deduction", "公积金扣款": "housing_fund_deduction", "个税": "income_tax", "个人所得税": "income_tax"}, "required_fields": ["employee_id"], "data_type": "deduction_detail"}}, "validation_rules": {"employee_id": {"required": true, "min_length": 1}, "employee_name": {"required": true, "min_length": 2}, "basic_salary": {"min_value": 0, "max_value": 100000}, "performance_bonus": {"min_value": 0, "max_value": 50000}}}